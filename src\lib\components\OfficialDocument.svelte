<script lang="ts">
  import { onMount } from 'svelte';

  // المدخلات
  export let subject: string = '';
  export let content: string = '';
  export let unitName: string = '';
  export let stateName: string = 'دولة ليبيا';  // اسم الدولة (يمكن تغييره)
  export let unitLogo: string | null = null;
  export let organizationLogo: string | null = null;
  export let stamp: string | null = null;
  export let signature: any = null;
  export let sender: any = null;
  export let printMode: boolean = false;
  export let referenceNumber: string = '';
  export let date: string = '';

  // حالة المكون
  let mounted = false;
  let logoError = false;
  let organizationLogoError = false;
  let stampError = false;

  // تحويل روابط صور Google Drive إلى روابط مباشرة
  function convertGoogleDriveUrl(url: string): string {
    if (!url) return '';

    // التحقق مما إذا كان الرابط من Google Drive
    if (url.includes('drive.google.com')) {
      // استخراج معرف الملف
      const fileIdMatch = url.match(/[-\w]{25,}/);
      if (fileIdMatch && fileIdMatch[0]) {
        return `https://drive.google.com/uc?export=view&id=${fileIdMatch[0]}`;
      }
    }

    return url;
  }

  // تنسيق التاريخ بالعربية
  function formatDate(dateString: string): string {
    if (!dateString) return '';

    try {
      const date = new Date(dateString);
      return new Intl.DateTimeFormat('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }).format(date);
    } catch (error) {
      console.error('Error formatting date:', error);
      return dateString;
    }
  }

  // تحميل الصور عند تركيب المكون
  onMount(() => {
    mounted = true;
  });

  // تحويل روابط الصور
  $: processedUnitLogo = convertGoogleDriveUrl(unitLogo || '');
  $: processedOrganizationLogo = convertGoogleDriveUrl(organizationLogo || '');
  $: processedStamp = convertGoogleDriveUrl(stamp || '');

  // تنسيق التاريخ
  $: formattedDate = formatDate(date);
</script>

<div class="official-document {printMode ? 'print-mode' : ''}" dir="rtl">
  <!-- رأس الوثيقة -->
  <div class="document-header">
    <div class="logo right-logo">
      {#if processedUnitLogo && mounted}
        <img
          src={processedUnitLogo}
          alt="شعار الوحدة"
          on:error={() => logoError = true}
          class:hidden={logoError}
        />
      {/if}
    </div>

    <div class="header-text">
      <h1>{stateName}</h1>
      <h2>{unitName || ''}</h2>
    </div>

    <div class="logo left-logo">
      {#if processedOrganizationLogo && mounted}
        <img
          src={processedOrganizationLogo}
          alt="شعار المؤسسة"
          on:error={() => organizationLogoError = true}
          class:hidden={organizationLogoError}
        />
      {/if}
    </div>
  </div>

  <!-- عنوان الوثيقة -->
  <div class="document-title">
    <h2>{subject}</h2>
  </div>

  <!-- معلومات الوثيقة -->
  <div class="document-info">
    <div class="info-item">
      <span class="info-label">التاريخ:</span>
      <span class="info-value">{formattedDate}</span>
    </div>

    <div class="info-item">
      <span class="info-label">الرقم الإشاري:</span>
      <span class="info-value">{referenceNumber}</span>
    </div>
  </div>

  <!-- محتوى الوثيقة -->
  <div class="document-content">
    {@html content}
  </div>

  <!-- تذييل الوثيقة -->
  <div class="document-footer">
    <div class="signature-area">
      {#if processedStamp && mounted}
        <div class="stamp">
          <img
            src={processedStamp}
            alt="ختم"
            on:error={() => stampError = true}
            class:hidden={stampError}
          />
        </div>
      {/if}

      <div class="signature-text">
        <div class="signature-title">التوقيع</div>
        {#if sender}
          <div class="sender-name">{sender.full_name}</div>
        {/if}
      </div>
    </div>

    <div class="issue-date">
      <div>صدر في: {formattedDate}</div>
    </div>
  </div>
</div>

<style>
  .official-document {
    width: 100%;
    max-width: 210mm; /* عرض A4 */
    margin: 0 auto;
    padding: 20mm;
    box-sizing: border-box;
    position: relative;
    font-family: 'Calibri', 'Arial', sans-serif;
    background-color: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
  }

  .print-mode {
    box-shadow: none;
    border: none;
    padding: 0;
  }

  /* رأس الوثيقة */
  .document-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #4a5568;
  }

  .logo {
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .logo img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
  }

  .header-text {
    text-align: center;
    flex: 1;
  }

  .header-text h1 {
    font-size: 24px;
    margin: 0 0 5px 0;
    font-weight: bold;
  }

  .header-text h2 {
    font-size: 18px;
    margin: 0;
    font-weight: bold;
  }

  /* عنوان الوثيقة */
  .document-title {
    text-align: center;
    margin: 20px 0;
  }

  .document-title h2 {
    font-size: 22px;
    font-weight: bold;
    display: inline-block;
    padding: 5px 30px;
    border-bottom: 2px solid #000;
    margin: 0;
  }

  /* معلومات الوثيقة */
  .document-info {
    display: flex;
    justify-content: space-between;
    margin: 20px 0;
    padding: 10px 0;
    border-top: 1px solid #4a5568;
    border-bottom: 1px solid #4a5568;
    position: relative;
    font-size: 14px;
    font-weight: bold;
  }

  .info-item {
    display: flex;
    align-items: center;
  }

  .info-label {
    margin-left: 5px;
    font-weight: bold;
  }

  /* محتوى الوثيقة */
  .document-content {
    min-height: 300px;
    margin-bottom: 30px;
    line-height: 1.6;
    text-align: justify;
    font-size: 16px;
  }

  /* تذييل الوثيقة */
  .document-footer {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #4a5568;
    position: relative;
  }

  .signature-area {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 20px;
  }

  .stamp {
    width: 80px;
    height: 80px;
    margin-bottom: 10px;
  }

  .stamp img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
  }

  .signature-text {
    text-align: center;
  }

  .signature-title {
    font-weight: bold;
    margin-bottom: 5px;
  }

  .sender-name {
    font-size: 16px;
    font-weight: bold;
  }

  .issue-date {
    text-align: left;
    font-size: 14px;
    font-weight: bold;
    margin-top: 20px;
  }

  .hidden {
    display: none;
  }

  /* تنسيقات الطباعة */
  @media print {
    .official-document {
      width: 210mm;
      height: 297mm;
      margin: 0;
      padding: 20mm;
      box-sizing: border-box;
      box-shadow: none;
      border: none;
      page-break-inside: avoid;
    }

    .document-content {
      page-break-inside: auto;
    }

    .document-footer {
      page-break-inside: avoid;
    }
  }
</style>
