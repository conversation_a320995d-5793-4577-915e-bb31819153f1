<script lang="ts">
  import { onMount } from 'svelte';
  import { supabase } from '$lib/supabase';
  import { goto } from '$app/navigation';

  interface SignedDocument {
    id: string;
    document_id: string;
    creator_id: string;
    signer_id: string;
    status: string;
    reference_number: string;
    signature: any;
    rejection_reason?: string;
    revision_comments?: string;
    signed_at?: string;
    created_at: string;
    updated_at: string;
    document_title?: string;
    document_content?: string;
    creator_name?: string;
    creator_email?: string;
    signer_name?: string;
    signer_email?: string;
    creator_unit_name?: string;
    signer_unit_name?: string;
  }

  let signedDocuments: SignedDocument[] = [];
  let loading = true;
  let error: string | null = null;
  let currentUserId: string | null = null;

  // فلترة المستندات
  let statusFilter = 'all';
  let roleFilter = 'all'; // 'creator' أو 'signer' أو 'all'

  const statusLabels: Record<string, string> = {
    'pending_signature': 'في انتظار التوقيع',
    'signed': 'موقع',
    'rejected': 'مرفوض',
    'revision_requested': 'مطلوب تعديل'
  };

  const statusColors: Record<string, string> = {
    'pending_signature': 'bg-yellow-100 text-yellow-800',
    'signed': 'bg-green-100 text-green-800',
    'rejected': 'bg-red-100 text-red-800',
    'revision_requested': 'bg-blue-100 text-blue-800'
  };

  onMount(async () => {
    await loadSignedDocuments();
  });

  async function loadSignedDocuments() {
    try {
      loading = true;
      error = null;

      // الحصول على المستخدم الحالي
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        error = 'يجب تسجيل الدخول لعرض المستندات';
        loading = false;
        return;
      }

      currentUserId = user.id;

      // جلب المستندات الموقعة مع التفاصيل
      const { data, error: fetchError } = await supabase
        .from('signed_documents_with_details')
        .select('*')
        .or(`creator_id.eq.${user.id},signer_id.eq.${user.id}`)
        .order('created_at', { ascending: false });

      if (fetchError) {
        console.error('خطأ في جلب المستندات:', fetchError);
        error = 'حدث خطأ أثناء جلب المستندات: ' + fetchError.message;
        return;
      }

      signedDocuments = data || [];
      console.log('تم جلب المستندات الموقعة:', signedDocuments);
    } catch (err: any) {
      console.error('خطأ في تحميل المستندات:', err);
      error = 'حدث خطأ أثناء تحميل المستندات: ' + (err.message || 'خطأ غير معروف');
    } finally {
      loading = false;
    }
  }

  // فلترة المستندات حسب الحالة والدور
  $: filteredDocuments = signedDocuments.filter(doc => {
    const statusMatch = statusFilter === 'all' || doc.status === statusFilter;
    const roleMatch = roleFilter === 'all' || 
      (roleFilter === 'creator' && doc.creator_id === currentUserId) ||
      (roleFilter === 'signer' && doc.signer_id === currentUserId);
    
    return statusMatch && roleMatch;
  });

  function viewDocument(doc: SignedDocument) {
    goto(`/dashboard/signed-documents/${doc.id}`);
  }

  function formatDate(dateString: string) {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  function getMyRole(doc: SignedDocument): string {
    if (doc.creator_id === currentUserId) return 'منشئ';
    if (doc.signer_id === currentUserId) return 'موقع';
    return 'غير محدد';
  }
</script>

<svelte:head>
  <title>المستندات الموقعة</title>
</svelte:head>

<div class="container mx-auto p-6 rtl">
  <div class="bg-white rounded-lg shadow-md">
    <!-- العنوان والفلاتر -->
    <div class="border-b border-gray-200 p-6">
      <div class="flex justify-between items-center mb-4">
        <h1 class="text-2xl font-bold text-gray-900">المستندات الموقعة</h1>
        <button
          class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md"
          on:click={loadSignedDocuments}
        >
          تحديث
        </button>
      </div>

      <!-- فلاتر -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label for="status-filter" class="block text-sm font-medium text-gray-700 mb-2">
            فلترة حسب الحالة
          </label>
          <select
            id="status-filter"
            bind:value={statusFilter}
            class="w-full p-2 border border-gray-300 rounded-md"
          >
            <option value="all">جميع الحالات</option>
            <option value="pending_signature">في انتظار التوقيع</option>
            <option value="signed">موقع</option>
            <option value="rejected">مرفوض</option>
            <option value="revision_requested">مطلوب تعديل</option>
          </select>
        </div>

        <div>
          <label for="role-filter" class="block text-sm font-medium text-gray-700 mb-2">
            فلترة حسب الدور
          </label>
          <select
            id="role-filter"
            bind:value={roleFilter}
            class="w-full p-2 border border-gray-300 rounded-md"
          >
            <option value="all">جميع الأدوار</option>
            <option value="creator">المستندات التي أنشأتها</option>
            <option value="signer">المستندات المطلوب توقيعها</option>
          </select>
        </div>
      </div>
    </div>

    <!-- المحتوى -->
    <div class="p-6">
      {#if loading}
        <div class="flex justify-center items-center py-12">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          <span class="mr-3 text-gray-600">جاري تحميل المستندات...</span>
        </div>
      {:else if error}
        <div class="bg-red-50 border border-red-200 rounded-md p-4">
          <div class="flex">
            <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
            </svg>
            <div class="mr-3">
              <h3 class="text-sm font-medium text-red-800">خطأ</h3>
              <p class="text-sm text-red-700 mt-1">{error}</p>
            </div>
          </div>
        </div>
      {:else if filteredDocuments.length === 0}
        <div class="text-center py-12">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">لا توجد مستندات</h3>
          <p class="mt-1 text-sm text-gray-500">لم يتم العثور على مستندات موقعة تطابق المعايير المحددة.</p>
        </div>
      {:else}
        <!-- قائمة المستندات -->
        <div class="space-y-4">
          {#each filteredDocuments as doc (doc.id)}
            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
              <div class="flex justify-between items-start">
                <div class="flex-1">
                  <div class="flex items-center space-x-2 space-x-reverse mb-2">
                    <h3 class="text-lg font-medium text-gray-900">{doc.document_title || 'مستند بدون عنوان'}</h3>
                    <span class="px-2 py-1 text-xs font-medium rounded-full {statusColors[doc.status]}">
                      {statusLabels[doc.status]}
                    </span>
                  </div>
                  
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                    <div>
                      <p><strong>الرقم المرجعي:</strong> {doc.reference_number}</p>
                      <p><strong>المنشئ:</strong> {doc.creator_name || 'غير محدد'}</p>
                      <p><strong>الموقع:</strong> {doc.signer_name || 'غير محدد'}</p>
                    </div>
                    <div>
                      <p><strong>دوري:</strong> {getMyRole(doc)}</p>
                      <p><strong>تاريخ الإنشاء:</strong> {formatDate(doc.created_at)}</p>
                      {#if doc.signed_at}
                        <p><strong>تاريخ التوقيع:</strong> {formatDate(doc.signed_at)}</p>
                      {/if}
                    </div>
                  </div>

                  {#if doc.rejection_reason}
                    <div class="mt-3 p-3 bg-red-50 border border-red-200 rounded-md">
                      <p class="text-sm text-red-800"><strong>سبب الرفض:</strong> {doc.rejection_reason}</p>
                    </div>
                  {/if}

                  {#if doc.revision_comments}
                    <div class="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-md">
                      <p class="text-sm text-blue-800"><strong>تعليقات التعديل:</strong> {doc.revision_comments}</p>
                    </div>
                  {/if}
                </div>

                <div class="mr-4">
                  <button
                    class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm"
                    on:click={() => viewDocument(doc)}
                  >
                    عرض التفاصيل
                  </button>
                </div>
              </div>
            </div>
          {/each}
        </div>
      {/if}
    </div>
  </div>
</div>

<style>
  .container {
    direction: rtl;
    text-align: right;
  }
</style>
