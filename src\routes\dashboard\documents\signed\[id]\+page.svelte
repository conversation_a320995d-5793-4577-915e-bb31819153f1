<script lang="ts">
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import { supabase } from '$lib/supabase';
  import { SignedDocumentService, type SignedDocument, type SignedDocumentHistory } from '$lib/services/signedDocumentService';

  export let data: { params: { id: string } };
  import SignedDocumentStatus from '$lib/components/SignedDocumentStatus.svelte';

  let loading: boolean = true;
  let error: string | null = null;
  let signedDocument: SignedDocument | null = null;
  let documentHistory: SignedDocumentHistory[] = [];
  let currentUser: any = null;
  let showSignModal: boolean = false;
  let signaturePassword: string = '';
  let isSigning: boolean = false;
  let showSendModal: boolean = false;
  let recipientId: string = '';
  let recipientUnitId: string = '';
  let isSending: boolean = false;
  let showReviewModal: boolean = false;
  let reviewAction: 'approved' | 'rejected' | 'returned_for_edit' = 'approved';
  let reviewNotes: string = '';
  let isReviewing: boolean = false;
  let users: any[] = [];
  let units: any[] = [];

  // دالة لجلب المستند الموقع
  async function fetchSignedDocument() {
    try {
      loading = true;
      error = null;

      // التحقق من وجود المستخدم
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        error = 'يجب تسجيل الدخول لعرض المستند الموقع';
        return;
      }

      currentUser = user;

      // جلب المستند الموقع
      const documentId = data.params.id;
      signedDocument = await SignedDocumentService.getSignedDocumentById(documentId);

      if (!signedDocument) {
        error = 'لم يتم العثور على المستند الموقع';
        return;
      }

      // جلب تاريخ المستند
      documentHistory = await SignedDocumentService.getSignedDocumentHistory(documentId);

      // جلب المستخدمين والوحدات
      const { data: usersData } = await supabase
        .from('profiles')
        .select('id, full_name, email')
        .order('full_name');

      users = usersData || [];

      const { data: unitsData } = await supabase
        .from('units')
        .select('id, name')
        .order('name');

      units = unitsData || [];
    } catch (err) {
      console.error('Exception in fetchSignedDocument:', err);
      error = 'حدث خطأ أثناء جلب المستند الموقع';
    } finally {
      loading = false;
    }
  }

  // دالة لتوقيع المستند
  async function signDocument() {
    try {
      if (!signedDocument || !currentUser) {
        alert('لم يتم العثور على المستند أو المستخدم');
        return;
      }

      isSigning = true;

      // التحقق من أن المستخدم هو الموقع المطلوب
      if (signedDocument.signer_id !== currentUser.id) {
        alert('ليس لديك صلاحية توقيع هذا المستند');
        isSigning = false;
        return;
      }

      // توقيع المستند
      const result = await SignedDocumentService.signDocument(
        signedDocument.id,
        currentUser.id,
        signedDocument.document?.content || '',
        signedDocument.document?.title || '',
        signaturePassword
      );

      if (result) {
        alert('تم توقيع المستند بنجاح');
        showSignModal = false;
        signaturePassword = '';
        fetchSignedDocument();
      } else {
        alert('حدث خطأ أثناء توقيع المستند');
      }
    } catch (err) {
      console.error('Error signing document:', err);
      alert('حدث خطأ أثناء توقيع المستند');
    } finally {
      isSigning = false;
    }
  }

  // دالة لإرسال المستند الموقع
  async function sendSignedDocument() {
    try {
      if (!signedDocument || !currentUser) {
        alert('لم يتم العثور على المستند أو المستخدم');
        return;
      }

      isSending = true;

      // التحقق من أن المستند موقع
      if (signedDocument.status !== 'signed') {
        alert('يجب توقيع المستند قبل إرساله');
        isSending = false;
        return;
      }

      // التحقق من تحديد مستلم أو وحدة مستلمة
      if (!recipientId && !recipientUnitId) {
        alert('يجب تحديد مستلم أو وحدة مستلمة');
        isSending = false;
        return;
      }

      // إرسال المستند
      const result = await SignedDocumentService.sendSignedDocument(
        signedDocument.id,
        recipientId || undefined,
        recipientUnitId || undefined
      );

      if (result) {
        alert('تم إرسال المستند بنجاح');
        showSendModal = false;
        recipientId = '';
        recipientUnitId = '';
        fetchSignedDocument();
      } else {
        alert('حدث خطأ أثناء إرسال المستند');
      }
    } catch (err) {
      console.error('Error sending signed document:', err);
      alert('حدث خطأ أثناء إرسال المستند');
    } finally {
      isSending = false;
    }
  }

  // دالة لمراجعة المستند
  async function reviewDocument() {
    try {
      if (!signedDocument || !currentUser) {
        alert('لم يتم العثور على المستند أو المستخدم');
        return;
      }

      isReviewing = true;

      // التحقق من أن المستند قيد المراجعة
      if (signedDocument.status !== 'under_review') {
        alert('المستند ليس قيد المراجعة');
        isReviewing = false;
        return;
      }

      // التحقق من إدخال ملاحظات المراجعة إذا كان الإجراء رفض أو إرجاع للتعديل
      if ((reviewAction === 'rejected' || reviewAction === 'returned_for_edit') && !reviewNotes) {
        alert('يجب إدخال سبب ' + (reviewAction === 'rejected' ? 'الرفض' : 'الإرجاع للتعديل'));
        isReviewing = false;
        return;
      }

      // مراجعة المستند
      const result = await SignedDocumentService.reviewSignedDocument(
        signedDocument.id,
        currentUser.id,
        reviewAction,
        reviewNotes
      );

      if (result) {
        alert('تمت مراجعة المستند بنجاح');
        showReviewModal = false;
        reviewNotes = '';
        fetchSignedDocument();
      } else {
        alert('حدث خطأ أثناء مراجعة المستند');
      }
    } catch (err) {
      console.error('Error reviewing document:', err);
      alert('حدث خطأ أثناء مراجعة المستند');
    } finally {
      isReviewing = false;
    }
  }

  // دالة لتنسيق التاريخ
  function formatDate(dateString: string): string {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-LY', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  // تحميل البيانات عند تحميل الصفحة
  onMount(fetchSignedDocument);
</script>

<div class="container mx-auto p-4 rtl" dir="rtl">
  {#if loading}
    <div class="flex justify-center items-center h-64">
      <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
    </div>
  {:else if error}
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
      <p>{error}</p>
    </div>
  {:else if signedDocument}
    <div class="flex justify-between items-center mb-6">
      <div>
        <h1 class="text-3xl font-bold">{signedDocument.document?.title || 'مستند بدون عنوان'}</h1>
        <div class="mt-2">
          <SignedDocumentStatus status={signedDocument.status} />
        </div>
      </div>
      <div class="flex space-x-2 space-x-reverse">
        <button
          class="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded"
          on:click={() => goto('/dashboard/documents/signed')}
        >
          العودة للقائمة
        </button>

        {#if signedDocument.status === 'pending_signature' && signedDocument.signer_id === currentUser?.id}
          <button
            class="bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded"
            on:click={() => showSignModal = true}
          >
            توقيع المستند
          </button>
        {/if}

        {#if signedDocument.status === 'signed' && (signedDocument.creator_id === currentUser?.id || signedDocument.signer_id === currentUser?.id)}
          <button
            class="bg-purple-500 hover:bg-purple-600 text-white py-2 px-4 rounded"
            on:click={() => showSendModal = true}
          >
            إرسال المستند
          </button>
        {/if}

        {#if signedDocument.status === 'under_review' && (signedDocument.recipient_id === currentUser?.id || currentUser?.unit_id === signedDocument.recipient_unit_id)}
          <button
            class="bg-yellow-500 hover:bg-yellow-600 text-white py-2 px-4 rounded"
            on:click={() => showReviewModal = true}
          >
            مراجعة المستند
          </button>
        {/if}
      </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <!-- معلومات المستند -->
      <div class="md:col-span-2 bg-white rounded-lg shadow p-6">
        <h2 class="text-xl font-bold mb-4">معلومات المستند</h2>
        <div class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p class="text-gray-600">العنوان:</p>
              <p class="font-medium">{signedDocument.document?.title || '-'}</p>
            </div>
            <div>
              <p class="text-gray-600">الرقم الإشاري:</p>
              <p class="font-medium">{signedDocument.reference_number || '-'}</p>
            </div>
            <div>
              <p class="text-gray-600">تاريخ الإنشاء:</p>
              <p class="font-medium">{formatDate(signedDocument.created_at)}</p>
            </div>
            <div>
              <p class="text-gray-600">تاريخ التوقيع:</p>
              <p class="font-medium">{signedDocument.signed_at ? formatDate(signedDocument.signed_at) : '-'}</p>
            </div>
            <div>
              <p class="text-gray-600">المنشئ:</p>
              <p class="font-medium">{signedDocument.creator?.full_name || '-'}</p>
            </div>
            <div>
              <p class="text-gray-600">الموقع:</p>
              <p class="font-medium">{signedDocument.signer?.full_name || '-'}</p>
            </div>
            <div>
              <p class="text-gray-600">المستلم:</p>
              <p class="font-medium">
                {#if signedDocument.recipient}
                  {signedDocument.recipient.full_name}
                {:else if signedDocument.recipient_unit}
                  {signedDocument.recipient_unit.name} (وحدة)
                {:else}
                  -
                {/if}
              </p>
            </div>
            <div>
              <p class="text-gray-600">المراجع:</p>
              <p class="font-medium">{signedDocument.reviewer?.full_name || '-'}</p>
            </div>
          </div>

          {#if signedDocument.review_notes}
            <div class="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
              <p class="font-bold text-yellow-800">ملاحظات المراجعة:</p>
              <p class="mt-1 text-yellow-800">{signedDocument.review_notes}</p>
            </div>
          {/if}
        </div>
      </div>

      <!-- تاريخ المستند -->
      <div class="bg-white rounded-lg shadow p-6">
        <h2 class="text-xl font-bold mb-4">تاريخ المستند</h2>
        {#if documentHistory.length === 0}
          <p class="text-gray-500">لا يوجد تاريخ للمستند</p>
        {:else}
          <div class="space-y-4">
            {#each documentHistory as historyItem}
              <div class="border-r-2 border-blue-500 pr-4 relative">
                <div class="absolute right-[-8px] top-0 w-4 h-4 rounded-full bg-blue-500"></div>
                <p class="font-medium">{formatDate(historyItem.created_at)}</p>
                <p class="text-gray-600">
                  {#if historyItem.action === 'create'}
                    تم إنشاء المستند
                  {:else if historyItem.action === 'request_signature'}
                    تم طلب التوقيع
                  {:else if historyItem.action === 'sign'}
                    تم توقيع المستند
                  {:else if historyItem.action === 'send'}
                    تم إرسال المستند
                  {:else if historyItem.action === 'review'}
                    تم استلام المستند للمراجعة
                  {:else if historyItem.action === 'approve'}
                    تمت الموافقة على المستند
                  {:else if historyItem.action === 'reject'}
                    تم رفض المستند
                  {:else if historyItem.action === 'return_for_edit'}
                    تم إرجاع المستند للتعديل
                  {:else}
                    تم تحديث المستند
                  {/if}
                </p>
                <p class="text-sm text-gray-500">بواسطة: {historyItem.user?.full_name || '-'}</p>
              </div>
            {/each}
          </div>
        {/if}
      </div>
    </div>

    <!-- محتوى المستند -->
    <div class="mt-6 bg-white rounded-lg shadow p-6">
      <h2 class="text-xl font-bold mb-4">محتوى المستند</h2>
      <div class="border border-gray-200 rounded-lg p-4 min-h-[300px]">
        {#if signedDocument.document?.content}
          <div class="prose max-w-none rtl">
            {@html signedDocument.document.content}
          </div>
        {:else}
          <p class="text-gray-500">لا يوجد محتوى للمستند</p>
        {/if}
      </div>
    </div>
  {:else}
    <div class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4">
      <p>لم يتم العثور على المستند</p>
    </div>
  {/if}
</div>

<!-- نافذة توقيع المستند -->
{#if showSignModal}
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 w-full max-w-md">
      <h2 class="text-xl font-bold mb-4">توقيع المستند</h2>

      {#if signedDocument?.password_protected}
        <div class="mb-4">
          <label for="signature-password" class="block text-gray-700 mb-2">كلمة مرور التوقيع</label>
          <input
            type="password"
            id="signature-password"
            bind:value={signaturePassword}
            class="w-full p-2 border border-gray-300 rounded"
            placeholder="أدخل كلمة مرور التوقيع"
          />
        </div>
      {/if}

      <div class="flex justify-end mt-4">
        <button
          class="bg-gray-300 hover:bg-gray-400 text-gray-800 py-2 px-4 rounded ml-2"
          on:click={() => showSignModal = false}
        >
          إلغاء
        </button>
        <button
          class="bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded"
          on:click={signDocument}
          disabled={isSigning}
        >
          {isSigning ? 'جاري التوقيع...' : 'توقيع المستند'}
        </button>
      </div>
    </div>
  </div>
{/if}

<!-- نافذة إرسال المستند -->
{#if showSendModal}
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 w-full max-w-md">
      <h2 class="text-xl font-bold mb-4">إرسال المستند</h2>

      <div class="mb-4">
        <label for="recipient-type" class="block text-gray-700 mb-2">نوع المستلم</label>
        <div class="flex space-x-4 space-x-reverse">
          <label class="inline-flex items-center">
            <input
              type="radio"
              name="recipient-type"
              class="ml-2"
              checked={!!recipientId}
              on:change={() => { recipientId = users[0]?.id || ''; recipientUnitId = ''; }}
            />
            <span>مستخدم</span>
          </label>
          <label class="inline-flex items-center">
            <input
              type="radio"
              name="recipient-type"
              class="ml-2"
              checked={!!recipientUnitId}
              on:change={() => { recipientUnitId = units[0]?.id || ''; recipientId = ''; }}
            />
            <span>وحدة</span>
          </label>
        </div>
      </div>

      {#if recipientId}
        <div class="mb-4">
          <label for="recipient-id" class="block text-gray-700 mb-2">المستلم</label>
          <select
            id="recipient-id"
            bind:value={recipientId}
            class="w-full p-2 border border-gray-300 rounded"
          >
            <option value="">-- اختر المستلم --</option>
            {#each users as user}
              <option value={user.id}>{user.full_name}</option>
            {/each}
          </select>
        </div>
      {:else}
        <div class="mb-4">
          <label for="recipient-unit-id" class="block text-gray-700 mb-2">الوحدة المستلمة</label>
          <select
            id="recipient-unit-id"
            bind:value={recipientUnitId}
            class="w-full p-2 border border-gray-300 rounded"
          >
            <option value="">-- اختر الوحدة --</option>
            {#each units as unit}
              <option value={unit.id}>{unit.name}</option>
            {/each}
          </select>
        </div>
      {/if}

      <div class="flex justify-end mt-4">
        <button
          class="bg-gray-300 hover:bg-gray-400 text-gray-800 py-2 px-4 rounded ml-2"
          on:click={() => showSendModal = false}
        >
          إلغاء
        </button>
        <button
          class="bg-purple-500 hover:bg-purple-600 text-white py-2 px-4 rounded"
          on:click={sendSignedDocument}
          disabled={isSending || (!recipientId && !recipientUnitId)}
        >
          {isSending ? 'جاري الإرسال...' : 'إرسال المستند'}
        </button>
      </div>
    </div>
  </div>
{/if}

<!-- نافذة مراجعة المستند -->
{#if showReviewModal}
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 w-full max-w-md">
      <h2 class="text-xl font-bold mb-4">مراجعة المستند</h2>

      <div class="mb-4">
        <label for="review-action" class="block text-gray-700 mb-2">الإجراء</label>
        <div class="space-y-2">
          <label class="inline-flex items-center">
            <input
              type="radio"
              name="review-action"
              class="ml-2"
              checked={reviewAction === 'approved'}
              on:change={() => reviewAction = 'approved'}
            />
            <span>موافقة</span>
          </label>
          <label class="inline-flex items-center">
            <input
              type="radio"
              name="review-action"
              class="ml-2"
              checked={reviewAction === 'rejected'}
              on:change={() => reviewAction = 'rejected'}
            />
            <span>رفض</span>
          </label>
          <label class="inline-flex items-center">
            <input
              type="radio"
              name="review-action"
              class="ml-2"
              checked={reviewAction === 'returned_for_edit'}
              on:change={() => reviewAction = 'returned_for_edit'}
            />
            <span>إرجاع للتعديل</span>
          </label>
        </div>
      </div>

      {#if reviewAction === 'rejected' || reviewAction === 'returned_for_edit'}
        <div class="mb-4">
          <label for="review-notes" class="block text-gray-700 mb-2">
            {reviewAction === 'rejected' ? 'سبب الرفض' : 'سبب الإرجاع للتعديل'}
          </label>
          <textarea
            id="review-notes"
            bind:value={reviewNotes}
            class="w-full p-2 border border-gray-300 rounded"
            rows="4"
            placeholder={reviewAction === 'rejected' ? 'أدخل سبب الرفض' : 'أدخل سبب الإرجاع للتعديل'}
          ></textarea>
        </div>
      {/if}

      <div class="flex justify-end mt-4">
        <button
          class="bg-gray-300 hover:bg-gray-400 text-gray-800 py-2 px-4 rounded ml-2"
          on:click={() => showReviewModal = false}
        >
          إلغاء
        </button>
        <button
          class="bg-yellow-500 hover:bg-yellow-600 text-white py-2 px-4 rounded"
          on:click={reviewDocument}
          disabled={isReviewing || ((reviewAction === 'rejected' || reviewAction === 'returned_for_edit') && !reviewNotes)}
        >
          {isReviewing ? 'جاري المراجعة...' : 'تأكيد المراجعة'}
        </button>
      </div>
    </div>
  </div>
{/if}
