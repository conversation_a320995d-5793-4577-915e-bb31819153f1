<script lang="ts">
  import { onMount } from 'svelte';
  import { supabase } from '$lib/supabase';
  import { goto } from '$app/navigation';
  import { SignedDocumentService, type SignedDocument } from '$lib/services/signedDocumentService';
  import SignedDocumentStatus from '$lib/components/SignedDocumentStatus.svelte';

  let loading: boolean = true;
  let error: string | null = null;
  let signedDocuments: SignedDocument[] = [];
  let currentUser: any = null;
  let userRole: string = '';
  let activeTab: 'pending' | 'signed' | 'sent' | 'received' = 'pending';

  // دالة لجلب المستندات الموقعة
  async function fetchSignedDocuments() {
    try {
      console.log('Starting fetchSignedDocuments...');
      loading = true;
      error = null;

      // التحقق من وجود المستخدم
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError) {
        console.error('Error getting user:', userError);
        error = 'حدث خطأ أثناء التحقق من المستخدم: ' + userError.message;
        return;
      }

      if (!user) {
        console.error('No user found');
        error = 'يجب تسجيل الدخول لعرض المستندات الموقعة';
        return;
      }

      console.log('Current user:', user.id);
      currentUser = user;

      // محاولة استخدام استعلام مبسط أولاً
      try {
        console.log('Trying simple query first...');
        const { data: simpleData, error: simpleError } = await supabase
          .from('signed_documents')
          .select('id, document_id, creator_id, signer_id, status, reference_number, created_at')
          .limit(20);

        if (simpleError) {
          console.error('Simple query error:', simpleError);
          throw simpleError;
        }

        console.log('Simple query successful, got', simpleData?.length || 0, 'documents');

        if (simpleData && simpleData.length > 0) {
          // جلب المستندات المرتبطة
          const documentIds = simpleData.map(doc => doc.document_id).filter(Boolean);
          let documentsMap: Record<string, any> = {};

          if (documentIds.length > 0) {
            console.log('Fetching related documents...');
            const { data: documentsData, error: documentsError } = await supabase
              .from('documents')
              .select('id, title')
              .in('id', documentIds);

            if (!documentsError && documentsData) {
              console.log('Got related documents:', documentsData.length);
              documentsData.forEach((doc: any) => {
                documentsMap[doc.id] = doc;
              });
            } else {
              console.error('Error fetching related documents:', documentsError);
            }
          }

          // جلب بيانات المنشئين والموقعين
          const userIds = [...new Set([
            ...simpleData.map(doc => doc.creator_id).filter(Boolean),
            ...simpleData.map(doc => doc.signer_id).filter(Boolean)
          ])];

          let usersMap: Record<string, any> = {};

          if (userIds.length > 0) {
            console.log('Fetching related users...');
            const { data: usersData, error: usersError } = await supabase
              .from('profiles')
              .select('id, full_name, email')
              .in('id', userIds);

            if (!usersError && usersData) {
              console.log('Got related users:', usersData.length);
              usersData.forEach((user: any) => {
                usersMap[user.id] = user;
              });
            } else {
              console.error('Error fetching related users:', usersError);
            }
          }

          // إضافة المعلومات المرتبطة إلى المستندات الموقعة
          const enhancedData = simpleData.map((doc: any) => ({
            ...doc,
            signed_at: doc.signed_at || null,
            signature: doc.signature || null,
            recipient_id: doc.recipient_id || null,
            recipient_unit_id: doc.recipient_unit_id || null,
            reviewer_id: doc.reviewer_id || null,
            reviewed_at: doc.reviewed_at || null,
            review_notes: doc.review_notes || null,
            password_protected: doc.password_protected || false,
            document: documentsMap[doc.document_id] || { title: 'مستند غير معروف' },
            creator: usersMap[doc.creator_id] || { full_name: 'غير معروف' },
            signer: usersMap[doc.signer_id] || { full_name: 'غير معروف' }
          }));

          // تصفية المستندات حسب التبويب النشط
          let filteredData = enhancedData;

          switch (activeTab) {
            case 'pending':
              // المستندات التي تنتظر توقيعي
              filteredData = filteredData.filter((doc: any) =>
                doc.signer_id === user.id && doc.status === 'pending_signature'
              );
              break;
            case 'signed':
              // المستندات التي قمت بتوقيعها
              filteredData = filteredData.filter((doc: any) =>
                doc.signer_id === user.id && doc.status === 'signed'
              );
              break;
            case 'sent':
              // المستندات التي أرسلتها للتوقيع
              filteredData = filteredData.filter((doc: any) =>
                doc.creator_id === user.id
              );
              break;
            case 'received':
              // المستندات التي استلمتها للمراجعة
              filteredData = filteredData.filter((doc: any) =>
                (doc.recipient_id === user.id || doc.recipient_unit_id) && doc.status === 'under_review'
              );
              break;
          }

          console.log('Filtered data:', filteredData.length);
          signedDocuments = filteredData as SignedDocument[];
          return;
        }
      } catch (simpleErr) {
        console.error('Error in simple query approach:', simpleErr);
        // استمر إلى الطريقة الأصلية
      }

      // جلب دور المستخدم
      const { data: profile } = await supabase
        .from('profiles')
        .select('role, role_id, unit_id')
        .eq('id', user.id)
        .single();

      if (profile) {
        userRole = profile.role || '';
        console.log('User role:', userRole, 'Unit ID:', profile.unit_id);
      }

      // جلب المستندات الموقعة حسب التبويب النشط
      console.log('Using original query approach...');
      let query = supabase
        .from('signed_documents')
        .select(`
          id, document_id, creator_id, signer_id, status, reference_number, created_at,
          document:document_id(id, title),
          creator:creator_id(id, full_name),
          signer:signer_id(id, full_name)
        `);

      switch (activeTab) {
        case 'pending':
          // المستندات التي تنتظر توقيعي
          console.log('Filtering for pending documents');
          query = query.eq('signer_id', user.id).eq('status', 'pending_signature');
          break;
        case 'signed':
          // المستندات التي قمت بتوقيعها
          console.log('Filtering for signed documents');
          query = query.eq('signer_id', user.id).eq('status', 'signed');
          break;
        case 'sent':
          // المستندات التي أرسلتها للتوقيع
          console.log('Filtering for sent documents');
          query = query.eq('creator_id', user.id);
          break;
        case 'received':
          // المستندات التي استلمتها للمراجعة
          console.log('Filtering for received documents');
          if (profile?.unit_id) {
            query = query.or(`recipient_id.eq.${user.id},recipient_unit_id.eq.${profile.unit_id}`).eq('status', 'under_review');
          } else {
            query = query.eq('recipient_id', user.id).eq('status', 'under_review');
          }
          break;
      }

      console.log('Executing query...');
      const { data, error: fetchError } = await query.order('created_at', { ascending: false }).limit(20);

      if (fetchError) {
        console.error('Error fetching signed documents:', fetchError);
        error = 'حدث خطأ أثناء جلب المستندات الموقعة: ' + fetchError.message;
        return;
      }

      console.log('Query successful, got', data?.length || 0, 'documents');

      // Asegurarse de que los datos cumplen con el tipo SignedDocument
      if (data) {
        // Añadir propiedades faltantes si es necesario
        const completeData = data.map((doc: any) => ({
          ...doc,
          signed_at: doc.signed_at || null,
          signature: doc.signature || null,
          recipient_id: doc.recipient_id || null,
          recipient_unit_id: doc.recipient_unit_id || null,
          reviewer_id: doc.reviewer_id || null,
          reviewed_at: doc.reviewed_at || null,
          review_notes: doc.review_notes || null,
          password_protected: doc.password_protected || false
        }));
        signedDocuments = completeData as SignedDocument[];
      } else {
        signedDocuments = [];
      }
    } catch (err: any) {
      console.error('Exception in fetchSignedDocuments:', err);
      error = 'حدث خطأ أثناء جلب المستندات الموقعة: ' + (err.message || 'خطأ غير معروف');
    } finally {
      loading = false;
      console.log('fetchSignedDocuments completed. loading:', loading, 'error:', error, 'documents:', signedDocuments.length);
    }
  }

  // دالة لتوقيع مستند
  async function signDocument(signedDocId: string) {
    try {
      if (!currentUser) {
        alert('يجب تسجيل الدخول لتوقيع المستند');
        return;
      }

      // جلب المستند الموقع
      const signedDoc = await SignedDocumentService.getSignedDocumentById(signedDocId);
      if (!signedDoc || !signedDoc.document) {
        alert('لم يتم العثور على المستند');
        return;
      }

      // التحقق من أن المستخدم هو الموقع المطلوب
      if (signedDoc.signer_id !== currentUser.id) {
        alert('ليس لديك صلاحية توقيع هذا المستند');
        return;
      }

      // طلب كلمة المرور إذا كان المستند محمي بكلمة مرور
      let password = '';
      if (signedDoc.password_protected) {
        password = prompt('يرجى إدخال كلمة مرور التوقيع') || '';
        if (!password) {
          return;
        }
      }

      // توقيع المستند
      const result = await SignedDocumentService.signDocument(
        signedDocId,
        currentUser.id,
        signedDoc.document.content || '',
        signedDoc.document.title || '',
        password
      );

      if (result) {
        alert('تم توقيع المستند بنجاح');
        fetchSignedDocuments();
      } else {
        alert('حدث خطأ أثناء توقيع المستند');
      }
    } catch (err) {
      console.error('Error signing document:', err);
      alert('حدث خطأ أثناء توقيع المستند');
    }
  }

  // دالة لإرسال مستند موقع
  async function sendSignedDocument(signedDocId: string) {
    try {
      if (!currentUser) {
        alert('يجب تسجيل الدخول لإرسال المستند');
        return;
      }

      // جلب المستند الموقع
      const signedDoc = await SignedDocumentService.getSignedDocumentById(signedDocId);
      if (!signedDoc) {
        alert('لم يتم العثور على المستند');
        return;
      }

      // التحقق من أن المستند موقع
      if (signedDoc.status !== 'signed') {
        alert('يجب توقيع المستند قبل إرساله');
        return;
      }

      // طلب معلومات المستلم
      const recipientId = prompt('أدخل معرف المستلم (اتركه فارغاً إذا كنت ترسل إلى وحدة)');
      const recipientUnitId = !recipientId ? prompt('أدخل معرف وحدة المستلم') : null;

      if (!recipientId && !recipientUnitId) {
        alert('يجب تحديد مستلم أو وحدة مستلمة');
        return;
      }

      // إرسال المستند
      const result = await SignedDocumentService.sendSignedDocument(
        signedDocId,
        recipientId || undefined,
        recipientUnitId || undefined
      );

      if (result) {
        alert('تم إرسال المستند بنجاح');
        fetchSignedDocuments();
      } else {
        alert('حدث خطأ أثناء إرسال المستند');
      }
    } catch (err) {
      console.error('Error sending signed document:', err);
      alert('حدث خطأ أثناء إرسال المستند');
    }
  }

  // دالة لمراجعة مستند (موافقة، رفض، إرجاع للتعديل)
  async function reviewDocument(signedDocId: string, action: 'approved' | 'rejected' | 'returned_for_edit') {
    try {
      if (!currentUser) {
        alert('يجب تسجيل الدخول لمراجعة المستند');
        return;
      }

      // طلب ملاحظات المراجعة
      let notes = '';
      if (action === 'rejected' || action === 'returned_for_edit') {
        notes = prompt('يرجى إدخال سبب ' + (action === 'rejected' ? 'الرفض' : 'الإرجاع للتعديل')) || '';
        if (!notes) {
          alert('يجب إدخال سبب ' + (action === 'rejected' ? 'الرفض' : 'الإرجاع للتعديل'));
          return;
        }
      }

      // مراجعة المستند
      const result = await SignedDocumentService.reviewSignedDocument(
        signedDocId,
        currentUser.id,
        action,
        notes
      );

      if (result) {
        alert('تمت مراجعة المستند بنجاح');
        fetchSignedDocuments();
      } else {
        alert('حدث خطأ أثناء مراجعة المستند');
      }
    } catch (err) {
      console.error('Error reviewing document:', err);
      alert('حدث خطأ أثناء مراجعة المستند');
    }
  }

  // دالة لتنسيق التاريخ
  function formatDate(dateString: string): string {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-LY', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  // تغيير التبويب النشط
  function changeTab(tab: 'pending' | 'signed' | 'sent' | 'received') {
    activeTab = tab;
    fetchSignedDocuments();
  }

  // تحميل البيانات عند تحميل الصفحة
  onMount(fetchSignedDocuments);
</script>

<div class="container mx-auto p-4 rtl" dir="rtl">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-3xl font-bold">المستندات الموقعة</h1>
  </div>

  <!-- تبويبات المستندات -->
  <div class="border-b border-gray-200 mb-6">
    <nav class="flex -mb-px">
      <button
        class="py-2 px-4 border-b-2 {activeTab === 'pending' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}"
        on:click={() => changeTab('pending')}
      >
        في انتظار توقيعي
      </button>
      <button
        class="py-2 px-4 border-b-2 {activeTab === 'signed' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}"
        on:click={() => changeTab('signed')}
      >
        المستندات الموقعة
      </button>
      <button
        class="py-2 px-4 border-b-2 {activeTab === 'sent' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}"
        on:click={() => changeTab('sent')}
      >
        المستندات المرسلة
      </button>
      <button
        class="py-2 px-4 border-b-2 {activeTab === 'received' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}"
        on:click={() => changeTab('received')}
      >
        المستندات المستلمة
      </button>
    </nav>
  </div>

  {#if loading}
    <div class="flex justify-center items-center h-64">
      <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
    </div>
  {:else if error}
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
      <p>{error}</p>
    </div>
  {:else if signedDocuments.length === 0}
    <div class="bg-gray-100 border border-gray-300 text-gray-700 px-4 py-8 rounded mb-4 text-center">
      <p class="text-lg">لا توجد مستندات {activeTab === 'pending' ? 'في انتظار توقيعك' : activeTab === 'signed' ? 'موقعة' : activeTab === 'sent' ? 'مرسلة' : 'مستلمة'}</p>
    </div>
  {:else}
    <div class="overflow-x-auto">
      <table class="min-w-full bg-white border border-gray-200 rounded-lg overflow-hidden">
        <thead class="bg-gray-100">
          <tr>
            <th class="py-3 px-4 text-right">عنوان المستند</th>
            <th class="py-3 px-4 text-right">الحالة</th>
            <th class="py-3 px-4 text-right">الرقم الإشاري</th>
            <th class="py-3 px-4 text-right">
              {#if activeTab === 'pending' || activeTab === 'signed'}
                المنشئ
              {:else if activeTab === 'sent'}
                الموقع
              {:else}
                المرسل
              {/if}
            </th>
            <th class="py-3 px-4 text-right">تاريخ الإنشاء</th>
            <th class="py-3 px-4 text-right">الإجراءات</th>
          </tr>
        </thead>
        <tbody>
          {#each signedDocuments as doc}
            <tr class="border-t hover:bg-gray-50">
              <td class="py-3 px-4">{doc.document?.title || 'بدون عنوان'}</td>
              <td class="py-3 px-4">
                <SignedDocumentStatus status={doc.status} compact={true} />
              </td>
              <td class="py-3 px-4">{doc.reference_number || '-'}</td>
              <td class="py-3 px-4">
                {#if activeTab === 'pending' || activeTab === 'signed'}
                  {doc.creator?.full_name || '-'}
                {:else if activeTab === 'sent'}
                  {doc.signer?.full_name || '-'}
                {:else}
                  {doc.creator?.full_name || '-'}
                {/if}
              </td>
              <td class="py-3 px-4">{formatDate(doc.created_at)}</td>
              <td class="py-3 px-4">
                <div class="flex space-x-2 space-x-reverse">
                  <!-- عرض المستند -->
                  <button
                    class="bg-blue-500 hover:bg-blue-600 text-white py-1 px-2 rounded text-xs"
                    on:click={() => goto(`/dashboard/documents/${doc.document_id}`)}
                  >
                    عرض المستند
                  </button>

                  <!-- توقيع المستند -->
                  {#if activeTab === 'pending'}
                    <button
                      class="bg-green-500 hover:bg-green-600 text-white py-1 px-2 rounded text-xs"
                      on:click={() => signDocument(doc.id)}
                    >
                      توقيع
                    </button>
                  {/if}

                  <!-- إرسال المستند -->
                  {#if doc.status === 'signed' && doc.signer_id === currentUser?.id}
                    <button
                      class="bg-purple-500 hover:bg-purple-600 text-white py-1 px-2 rounded text-xs"
                      on:click={() => sendSignedDocument(doc.id)}
                    >
                      إرسال
                    </button>
                  {/if}

                  <!-- مراجعة المستند -->
                  {#if activeTab === 'received' && doc.status === 'under_review'}
                    <button
                      class="bg-green-500 hover:bg-green-600 text-white py-1 px-2 rounded text-xs"
                      on:click={() => reviewDocument(doc.id, 'approved')}
                    >
                      موافقة
                    </button>
                    <button
                      class="bg-red-500 hover:bg-red-600 text-white py-1 px-2 rounded text-xs"
                      on:click={() => reviewDocument(doc.id, 'rejected')}
                    >
                      رفض
                    </button>
                    <button
                      class="bg-yellow-500 hover:bg-yellow-600 text-white py-1 px-2 rounded text-xs"
                      on:click={() => reviewDocument(doc.id, 'returned_for_edit')}
                    >
                      إرجاع للتعديل
                    </button>
                  {/if}
                </div>
              </td>
            </tr>
          {/each}
        </tbody>
      </table>
    </div>
  {/if}
</div>
