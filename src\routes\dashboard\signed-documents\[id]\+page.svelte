<script lang="ts">
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import { supabase } from '$lib/supabase';
  import { goto } from '$app/navigation';
  import { createSignature, verifySignature, getSignatureInfo } from '$lib/utils/signatureUtils';
  import OfficialDocument from '$lib/components/OfficialDocument.svelte';

  interface SignedDocument {
    id: string;
    document_id: string;
    creator_id: string;
    signer_id: string;
    recipient_id?: string;
    recipient_unit_id?: string;
    status: string;
    reference_number: string;
    signature: any;
    rejection_reason?: string;
    revision_comments?: string;
    signed_at?: string;
    created_at: string;
    updated_at: string;
    document_title?: string;
    document_content?: string;
    creator_name?: string;
    creator_email?: string;
    signer_name?: string;
    signer_email?: string;
    creator_unit_name?: string;
    signer_unit_name?: string;
  }

  let signedDocument: SignedDocument | null = null;
  let loading = true;
  let error: string | null = null;
  let currentUserId: string | null = null;
  let isProcessing = false;

  // متغيرات للإجراءات
  let showRejectModal = false;
  let showRevisionModal = false;
  let rejectionReason = '';
  let revisionComments = '';

  // متغيرات التوقيع الإلكتروني
  let showSignatureModal = false;
  let signaturePassword = '';
  let signatureInfo: any = null;
  let isSignatureValid = false;

  // متغيرات التحقق من التوقيع
  let showVerifyModal = false;
  let verifyPassword = '';
  let verificationResult: any = null;

  const statusLabels: Record<string, string> = {
    'pending_signature': 'في انتظار التوقيع',
    'signed': 'موقع',
    'rejected': 'مرفوض',
    'revision_requested': 'مطلوب تعديل'
  };

  const statusColors: Record<string, string> = {
    'pending_signature': 'bg-yellow-100 text-yellow-800',
    'signed': 'bg-green-100 text-green-800',
    'rejected': 'bg-red-100 text-red-800',
    'revision_requested': 'bg-blue-100 text-blue-800'
  };

  onMount(async () => {
    await loadSignedDocument();
  });

  async function loadSignedDocument() {
    try {
      loading = true;
      error = null;

      const documentId = $page.params.id;
      if (!documentId) {
        error = 'معرف المستند غير صحيح';
        loading = false;
        return;
      }

      // الحصول على المستخدم الحالي
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        error = 'يجب تسجيل الدخول لعرض المستند';
        loading = false;
        return;
      }

      currentUserId = user.id;

      // جلب تفاصيل المستند الموقع
      let { data, error: fetchError } = await supabase
        .from('signed_documents_with_details')
        .select('*')
        .eq('id', documentId)
        .single();

      // إذا فشل الاستعلام مع view، جرب الجدول الأساسي
      if (fetchError && fetchError.message.includes('does not exist')) {
        console.log('View لا يوجد، جرب الجدول الأساسي...');
        const { data: basicData, error: basicError } = await supabase
          .from('signed_documents')
          .select('*')
          .eq('id', documentId)
          .single();

        data = basicData;
        fetchError = basicError;
      }

      if (fetchError) {
        console.error('خطأ في جلب المستند:', fetchError);
        error = 'حدث خطأ أثناء جلب المستند: ' + fetchError.message;
        return;
      }

      if (!data) {
        error = 'المستند غير موجود';
        return;
      }

      // التحقق من صلاحية الوصول
      if (data.creator_id !== user.id && data.signer_id !== user.id) {
        error = 'ليس لديك صلاحية لعرض هذا المستند';
        return;
      }

      signedDocument = data;
      console.log('تم جلب المستند الموقع:', signedDocument);
    } catch (err: any) {
      console.error('خطأ في تحميل المستند:', err);
      error = 'حدث خطأ أثناء تحميل المستند: ' + (err.message || 'خطأ غير معروف');
    } finally {
      loading = false;
    }
  }

  // فتح نافذة التوقيع الإلكتروني
  function openSignatureModal() {
    showSignatureModal = true;
    signaturePassword = '';
  }

  // إغلاق نافذة التوقيع الإلكتروني
  function closeSignatureModal() {
    showSignatureModal = false;
    signaturePassword = '';
  }

  // تنفيذ التوقيع الإلكتروني
  async function executeElectronicSignature() {
    if (!signedDocument || !currentUserId || !signaturePassword.trim()) {
      alert('يرجى إدخال كلمة مرور التوقيع');
      return;
    }

    try {
      isProcessing = true;

      // التأكد من أن المستخدم مسجل دخول
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        alert('يجب تسجيل الدخول لتنفيذ هذا الإجراء');
        return;
      }

      // الحصول على محتوى المستند
      const { data: documentData, error: docError } = await supabase
        .from('documents')
        .select('title, content')
        .eq('id', signedDocument.document_id)
        .single();

      if (docError || !documentData) {
        alert('خطأ في الحصول على بيانات المستند');
        return;
      }

      // إنشاء التوقيع الإلكتروني باستخدام HASH-SHA256
      const electronicSignature = createSignature(
        documentData.content,
        documentData.title,
        user,
        signedDocument.recipient_id,
        signedDocument.recipient_unit_id,
        signaturePassword
      );

      // تحديث المستند بالتوقيع الإلكتروني
      const { error: updateError } = await supabase
        .from('signed_documents')
        .update({
          status: 'signed',
          signed_at: new Date().toISOString(),
          signature: electronicSignature
        })
        .eq('id', signedDocument.id);

      if (updateError) {
        console.error('خطأ في التوقيع:', updateError);
        alert('حدث خطأ أثناء التوقيع: ' + updateError.message);
        return;
      }

      alert('تم إنشاء التوقيع الإلكتروني بنجاح!');
      closeSignatureModal();
      await loadSignedDocument(); // إعادة تحميل البيانات
    } catch (err: any) {
      console.error('خطأ في التوقيع:', err);
      alert('حدث خطأ أثناء التوقيع: ' + (err.message || 'خطأ غير معروف'));
    } finally {
      isProcessing = false;
    }
  }

  // دالة للتوقيع السريع (بدون كلمة مرور)
  async function approveAndSign() {
    openSignatureModal();
  }

  // التحقق من التوقيع الإلكتروني
  async function checkSignatureValidity() {
    if (signedDocument?.signature && signedDocument?.document_id) {
      try {
        // الحصول على محتوى المستند الأصلي من قاعدة البيانات
        const { data: documentData, error: docError } = await supabase
          .from('documents')
          .select('title, content')
          .eq('id', signedDocument.document_id)
          .single();

        if (docError || !documentData) {
          console.error('خطأ في الحصول على بيانات المستند للتحقق:', docError);
          isSignatureValid = false;
          signatureInfo = getSignatureInfo(signedDocument.signature);
          return;
        }

        console.log('بيانات المستند للتحقق:', {
          title: documentData.title,
          contentLength: documentData.content?.length || 0,
          signature: signedDocument.signature
        });

        const verification: any = verifySignature(
          documentData.content,
          documentData.title,
          signedDocument.signature,
          currentUserId || undefined
        );

        isSignatureValid = verification?.valid || false;
        signatureInfo = getSignatureInfo(signedDocument.signature);

        console.log('نتيجة التحقق من التوقيع:', verification);
        console.log('حالة صحة التوقيع:', isSignatureValid);
      } catch (error) {
        console.error('خطأ في التحقق من التوقيع:', error);
        isSignatureValid = false;
        signatureInfo = getSignatureInfo(signedDocument.signature);
      }
    } else {
      // إذا لم يكن هناك توقيع، اعرض معلومات التوقيع فقط
      if (signedDocument?.signature) {
        signatureInfo = getSignatureInfo(signedDocument.signature);
        isSignatureValid = false;
      }
    }
  }

  // التحقق من التوقيع عند تحميل المستند
  $: if (signedDocument) {
    checkSignatureValidity();
  }

  // فتح نافذة التحقق من التوقيع
  function openVerifyModal() {
    showVerifyModal = true;
    verifyPassword = '';
    verificationResult = null;
  }

  // إغلاق نافذة التحقق من التوقيع
  function closeVerifyModal() {
    showVerifyModal = false;
    verifyPassword = '';
    verificationResult = null;
  }

  // التحقق من التوقيع مع كلمة مرور
  async function verifySignatureWithPassword() {
    if (!signedDocument?.signature || !signedDocument?.document_id || !verifyPassword.trim()) {
      alert('يرجى إدخال كلمة مرور التحقق');
      return;
    }

    try {
      // الحصول على محتوى المستند الأصلي
      const { data: documentData, error: docError } = await supabase
        .from('documents')
        .select('title, content')
        .eq('id', signedDocument.document_id)
        .single();

      if (docError || !documentData) {
        alert('خطأ في الحصول على بيانات المستند');
        return;
      }

      // التحقق من التوقيع مع كلمة المرور
      const verification: any = verifySignature(
        documentData.content,
        documentData.title,
        signedDocument.signature,
        currentUserId || undefined,
        verifyPassword
      );

      verificationResult = verification;

      if (verification?.valid && verification?.isAuthenticated) {
        alert('✅ التوقيع صالح وتم التحقق من كلمة المرور بنجاح!');
        isSignatureValid = true;
      } else if (verification?.requiresPassword) {
        alert('❌ كلمة المرور غير صحيحة');
      } else {
        alert('❌ التوقيع غير صالح');
      }

      console.log('نتيجة التحقق مع كلمة المرور:', verification);
    } catch (error) {
      console.error('خطأ في التحقق من التوقيع:', error);
      alert('حدث خطأ أثناء التحقق من التوقيع');
    }
  }

  async function rejectDocument() {
    if (!signedDocument || !rejectionReason.trim()) return;

    try {
      isProcessing = true;

      // التحقق من وجود العمود أولاً
      console.log('محاولة رفض المستند مع السبب:', rejectionReason);

      const updateData: any = {
        status: 'rejected'
      };

      // إضافة rejection_reason فقط إذا كان متوفراً
      try {
        updateData.rejection_reason = rejectionReason;
      } catch (e) {
        console.warn('لا يمكن إضافة rejection_reason:', e);
      }

      // تحديث signature
      if (signedDocument.signature) {
        updateData.signature = {
          ...signedDocument.signature,
          status: 'rejected',
          rejectedAt: new Date().toISOString(),
          rejectedBy: currentUserId,
          rejectionReason: rejectionReason
        };
      }

      // التأكد من أن المستخدم مسجل دخول
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        alert('يجب تسجيل الدخول لتنفيذ هذا الإجراء');
        return;
      }

      const { error: updateError } = await supabase
        .from('signed_documents')
        .update(updateData)
        .eq('id', signedDocument.id);

      if (updateError) {
        console.error('خطأ في الرفض:', updateError);

        // إذا كانت المشكلة في العمود، جرب بدون rejection_reason
        if (updateError.message.includes('rejection_reason')) {
          console.log('محاولة الرفض بدون حفظ السبب...');
          const { error: retryError } = await supabase
            .from('signed_documents')
            .update({
              status: 'rejected',
              signature: {
                ...signedDocument.signature,
                status: 'rejected',
                rejectedAt: new Date().toISOString(),
                rejectedBy: currentUserId,
                rejectionReason: rejectionReason
              }
            })
            .eq('id', signedDocument.id);

          if (retryError) {
            alert('حدث خطأ أثناء رفض المستند: ' + retryError.message);
            return;
          }
        } else {
          alert('حدث خطأ أثناء رفض المستند: ' + updateError.message);
          return;
        }
      }

      alert('تم رفض المستند بنجاح!');
      showRejectModal = false;
      rejectionReason = '';
      await loadSignedDocument(); // إعادة تحميل البيانات
    } catch (err: any) {
      console.error('خطأ في الرفض:', err);
      alert('حدث خطأ أثناء رفض المستند: ' + (err.message || 'خطأ غير معروف'));
    } finally {
      isProcessing = false;
    }
  }

  async function requestRevision() {
    if (!signedDocument || !revisionComments.trim()) return;

    try {
      isProcessing = true;

      console.log('محاولة طلب التعديل مع التعليقات:', revisionComments);

      const updateData: any = {
        status: 'revision_requested'
      };

      // إضافة revision_comments فقط إذا كان متوفراً
      try {
        updateData.revision_comments = revisionComments;
      } catch (e) {
        console.warn('لا يمكن إضافة revision_comments:', e);
      }

      // تحديث signature
      if (signedDocument.signature) {
        updateData.signature = {
          ...signedDocument.signature,
          status: 'revision_requested',
          revisionRequestedAt: new Date().toISOString(),
          revisionRequestedBy: currentUserId,
          revisionComments: revisionComments
        };
      }

      // التأكد من أن المستخدم مسجل دخول
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        alert('يجب تسجيل الدخول لتنفيذ هذا الإجراء');
        return;
      }

      const { error: updateError } = await supabase
        .from('signed_documents')
        .update(updateData)
        .eq('id', signedDocument.id);

      if (updateError) {
        console.error('خطأ في طلب التعديل:', updateError);

        // إذا كانت المشكلة في العمود، جرب بدون revision_comments
        if (updateError.message.includes('revision_comments')) {
          console.log('محاولة طلب التعديل بدون حفظ التعليقات...');
          const { error: retryError } = await supabase
            .from('signed_documents')
            .update({
              status: 'revision_requested',
              signature: {
                ...signedDocument.signature,
                status: 'revision_requested',
                revisionRequestedAt: new Date().toISOString(),
                revisionRequestedBy: currentUserId,
                revisionComments: revisionComments
              }
            })
            .eq('id', signedDocument.id);

          if (retryError) {
            alert('حدث خطأ أثناء طلب التعديل: ' + retryError.message);
            return;
          }
        } else {
          alert('حدث خطأ أثناء طلب التعديل: ' + updateError.message);
          return;
        }
      }

      alert('تم طلب التعديل بنجاح!');
      showRevisionModal = false;
      revisionComments = '';
      await loadSignedDocument(); // إعادة تحميل البيانات
    } catch (err: any) {
      console.error('خطأ في طلب التعديل:', err);
      alert('حدث خطأ أثناء طلب التعديل: ' + (err.message || 'خطأ غير معروف'));
    } finally {
      isProcessing = false;
    }
  }

  function formatDate(dateString: string) {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  function getMyRole(): string {
    if (!signedDocument || !currentUserId) return 'غير محدد';
    if (signedDocument.creator_id === currentUserId) return 'منشئ';
    if (signedDocument.signer_id === currentUserId) return 'موقع';
    return 'غير محدد';
  }

  function canTakeAction(): boolean {
    if (!signedDocument || !currentUserId) return false;
    return signedDocument.signer_id === currentUserId && signedDocument.status === 'pending_signature';
  }
</script>

<svelte:head>
  <title>تفاصيل المستند الموقع</title>
</svelte:head>

<div class="container mx-auto p-6 rtl">
  <div class="bg-white rounded-lg shadow-md">
    <!-- العنوان والتنقل -->
    <div class="border-b border-gray-200 p-6">
      <div class="flex justify-between items-center mb-4">
        <h1 class="text-2xl font-bold text-gray-900">تفاصيل المستند الموقع</h1>
        <button
          class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md"
          on:click={() => goto('/dashboard/signed-documents')}
        >
          العودة للقائمة
        </button>
      </div>
    </div>

    <!-- المحتوى -->
    <div class="p-6">
      {#if loading}
        <div class="flex justify-center items-center py-12">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          <span class="mr-3 text-gray-600">جاري تحميل المستند...</span>
        </div>
      {:else if error}
        <div class="bg-red-50 border border-red-200 rounded-md p-4">
          <div class="flex">
            <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
            </svg>
            <div class="mr-3">
              <h3 class="text-sm font-medium text-red-800">خطأ</h3>
              <p class="text-sm text-red-700 mt-1">{error}</p>
            </div>
          </div>
        </div>
      {:else if signedDocument}
        <!-- معلومات المستند -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
          <!-- المعلومات الأساسية -->
          <div class="lg:col-span-2">
            <div class="bg-gray-50 rounded-lg p-6">
              <h2 class="text-xl font-semibold mb-4">معلومات المستند</h2>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p class="text-sm text-gray-600">عنوان المستند</p>
                  <p class="font-medium">{signedDocument.document_title || 'غير محدد'}</p>
                </div>
                <div>
                  <p class="text-sm text-gray-600">الرقم الإشاري</p>
                  <p class="font-medium">{signedDocument.reference_number}</p>
                </div>
                <div>
                  <p class="text-sm text-gray-600">المنشئ</p>
                  <p class="font-medium">{signedDocument.creator_name || signedDocument.creator_id || 'غير محدد'}</p>
                </div>
                <div>
                  <p class="text-sm text-gray-600">الموقع المطلوب</p>
                  <p class="font-medium">{signedDocument.signer_name || signedDocument.signer_id || 'غير محدد'}</p>
                </div>
                <div>
                  <p class="text-sm text-gray-600">تاريخ الإنشاء</p>
                  <p class="font-medium">{formatDate(signedDocument.created_at)}</p>
                </div>
                <div>
                  <p class="text-sm text-gray-600">دوري في هذا المستند</p>
                  <p class="font-medium">{getMyRole()}</p>
                </div>
              </div>
            </div>
          </div>

          <!-- حالة المستند والإجراءات -->
          <div>
            <div class="bg-gray-50 rounded-lg p-6">
              <h2 class="text-xl font-semibold mb-4">حالة المستند</h2>
              <div class="space-y-4">
                <div>
                  <span class="px-3 py-1 text-sm font-medium rounded-full {statusColors[signedDocument.status]}">
                    {statusLabels[signedDocument.status]}
                  </span>
                </div>

                {#if signedDocument.signed_at}
                  <div>
                    <p class="text-sm text-gray-600">تاريخ التوقيع</p>
                    <p class="font-medium">{formatDate(signedDocument.signed_at)}</p>
                  </div>
                {/if}

                <!-- معلومات التوقيع الإلكتروني -->
                {#if signedDocument.signature && signedDocument.status === 'signed'}
                  <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <h4 class="text-green-800 font-semibold mb-2 flex items-center gap-2">
                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M9 12l2 2 4-4"></path>
                        <path d="M21 12c.552 0 1-.448 1-1V5c0-.552-.448-1-1-1H3c-.552 0-1 .448-1 1v6c0 .552.448 1 1 1h18z"></path>
                        <path d="M3 12v6c0 .552.448 1 1 1h16c.552 0 1-.448 1-1v-6"></path>
                      </svg>
                      التوقيع الإلكتروني
                      {#if isSignatureValid}
                        <span class="text-green-600 text-sm">(صالح ✓)</span>
                      {:else if signedDocument.signature?.passwordProtected}
                        <span class="text-orange-600 text-sm">(يتطلب كلمة مرور 🔒)</span>
                      {:else}
                        <span class="text-red-600 text-sm">(غير صالح ✗)</span>
                      {/if}
                    </h4>

                    <div class="text-sm text-green-700 space-y-2">
                      <p><strong>الموقع:</strong> {signatureInfo?.details?.userName || signedDocument.signer_name || 'غير محدد'}</p>
                      <p><strong>البريد الإلكتروني:</strong> {signatureInfo?.details?.email || signedDocument.signer_email || 'غير محدد'}</p>
                      <p><strong>تاريخ التوقيع:</strong> {signatureInfo?.details?.date || (signedDocument.signed_at ? new Date(signedDocument.signed_at).toLocaleString('ar-SA') : 'غير محدد')}</p>
                      <p><strong>معرف التوقيع:</strong> {signatureInfo?.details?.signatureId || signedDocument.signature?.signatureId || 'غير محدد'}</p>
                      <p><strong>طريقة التوقيع:</strong> {signatureInfo?.verificationMethod || signedDocument.signature?.signatureMethod || 'HASH-SHA256'}</p>
                      <p><strong>مستوى الأمان:</strong> {signatureInfo?.securityLevel || (signedDocument.signature?.passwordProtected ? 'عالي' : 'متوسط')}</p>
                      {#if signatureInfo?.details?.passwordProtected || signedDocument.signature?.passwordProtected}
                        <p><strong>محمي بكلمة مرور:</strong> نعم 🔒</p>
                      {/if}
                    </div>

                    <!-- زر التحقق من التوقيع -->
                    {#if signedDocument.signature?.passwordProtected && !isSignatureValid}
                      <div class="mt-3">
                        <button
                          class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-3 rounded-md text-sm"
                          on:click={openVerifyModal}
                        >
                          🔍 التحقق من التوقيع بكلمة المرور
                        </button>
                      </div>
                    {/if}
                  </div>
                {/if}

                {#if canTakeAction()}
                  <div class="space-y-2">
                    <button
                      class="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-md disabled:opacity-50"
                      disabled={isProcessing}
                      on:click={approveAndSign}
                    >
                      {isProcessing ? 'جاري التوقيع...' : 'الموافقة والتوقيع'}
                    </button>
                    <button
                      class="w-full bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-md"
                      on:click={() => showRejectModal = true}
                    >
                      رفض المستند
                    </button>
                    <button
                      class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md"
                      on:click={() => showRevisionModal = true}
                    >
                      طلب تعديل
                    </button>
                  </div>
                {/if}
              </div>
            </div>
          </div>
        </div>

        <!-- عرض أسباب الرفض أو تعليقات التعديل -->
        {#if signedDocument.rejection_reason}
          <div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
            <h3 class="text-lg font-semibold text-red-800 mb-2">سبب الرفض</h3>
            <p class="text-red-700">{signedDocument.rejection_reason}</p>
          </div>
        {/if}

        {#if signedDocument.revision_comments}
          <div class="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
            <h3 class="text-lg font-semibold text-blue-800 mb-2">تعليقات التعديل</h3>
            <p class="text-blue-700">{signedDocument.revision_comments}</p>
          </div>
        {/if}

        <!-- روابط إضافية -->
        <div class="mb-6 flex space-x-4 space-x-reverse">
          <button
            class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md"
            on:click={async () => {
              if (!signedDocument) return;

              // البحث عن الرسالة التي تحتوي على هذا المستند كمرفق
              const { data: messages } = await supabase
                .from('messages')
                .select('id')
                .contains('attachment', { signature_request_id: signedDocument.id })
                .limit(1);

              if (messages && messages.length > 0) {
                goto(`/dashboard/messages/view/${messages[0].id}`);
              } else {
                alert('لم يتم العثور على الرسالة المرتبطة بهذا المستند');
              }
            }}
          >
            عرض الرسالة والمرفق
          </button>
        </div>

        <!-- محتوى المستند مع التوقيع الإلكتروني -->
        {#if signedDocument.document_content && signedDocument.document_title}
          <div class="border border-gray-300 rounded-lg overflow-hidden">
            <div class="bg-gray-100 px-4 py-2 border-b border-gray-300">
              <h3 class="text-lg font-semibold">المستند الرسمي</h3>
            </div>
            <div class="document-viewer">
              <OfficialDocument
                subject={signedDocument.document_title}
                content={signedDocument.document_content}
                unitName={signedDocument.signer_unit_name || 'الوحدة'}
                signature={signedDocument.signature}
                sender={{
                  full_name: signedDocument.signer_name,
                  email: signedDocument.signer_email
                }}
                printMode={false}
                referenceNumber={signedDocument.reference_number}
                date={signedDocument.signed_at || signedDocument.created_at}
              />
            </div>
          </div>
        {:else}
          <div class="border border-gray-300 rounded-lg p-6 text-center">
            <p class="text-gray-500">محتوى المستند غير متوفر. يمكنك عرض المستند الكامل من خلال الرسالة المرفقة.</p>
          </div>
        {/if}
      {/if}
    </div>
  </div>
</div>

<!-- نافذة رفض المستند -->
{#if showRejectModal}
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
      <h3 class="text-lg font-semibold mb-4">رفض المستند</h3>
      <div class="mb-4">
        <label for="rejection-reason" class="block text-sm font-medium text-gray-700 mb-2">
          سبب الرفض (مطلوب)
        </label>
        <textarea
          id="rejection-reason"
          bind:value={rejectionReason}
          class="w-full p-3 border border-gray-300 rounded-md"
          rows="4"
          placeholder="يرجى ذكر سبب رفض المستند..."
        ></textarea>
      </div>
      <div class="flex space-x-3 space-x-reverse">
        <button
          class="flex-1 bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-md disabled:opacity-50"
          disabled={!rejectionReason.trim() || isProcessing}
          on:click={rejectDocument}
        >
          {isProcessing ? 'جاري الرفض...' : 'رفض المستند'}
        </button>
        <button
          class="flex-1 bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-md"
          on:click={() => {
            showRejectModal = false;
            rejectionReason = '';
          }}
        >
          إلغاء
        </button>
      </div>
    </div>
  </div>
{/if}

<!-- نافذة طلب التعديل -->
{#if showRevisionModal}
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
      <h3 class="text-lg font-semibold mb-4">طلب تعديل</h3>
      <div class="mb-4">
        <label for="revision-comments" class="block text-sm font-medium text-gray-700 mb-2">
          تعليقات التعديل (مطلوب)
        </label>
        <textarea
          id="revision-comments"
          bind:value={revisionComments}
          class="w-full p-3 border border-gray-300 rounded-md"
          rows="4"
          placeholder="يرجى ذكر التعديلات المطلوبة..."
        ></textarea>
      </div>
      <div class="flex space-x-3 space-x-reverse">
        <button
          class="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md disabled:opacity-50"
          disabled={!revisionComments.trim() || isProcessing}
          on:click={requestRevision}
        >
          {isProcessing ? 'جاري الطلب...' : 'طلب التعديل'}
        </button>
        <button
          class="flex-1 bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-md"
          on:click={() => {
            showRevisionModal = false;
            revisionComments = '';
          }}
        >
          إلغاء
        </button>
      </div>
    </div>
  </div>
{/if}

<!-- نافذة التوقيع الإلكتروني -->
{#if showSignatureModal}
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
      <h3 class="text-lg font-semibold mb-4 flex items-center gap-2">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M9 12l2 2 4-4"></path>
          <path d="M21 12c.552 0 1-.448 1-1V5c0-.552-.448-1-1-1H3c-.552 0-1 .448-1 1v6c0 .552.448 1 1 1h18z"></path>
          <path d="M3 12v6c0 .552.448 1 1 1h16c.552 0 1-.448 1-1v-6"></path>
        </svg>
        التوقيع الإلكتروني
      </h3>

      <div class="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-md">
        <h4 class="font-semibold text-blue-800 mb-2">معلومات التوقيع</h4>
        <div class="text-sm text-blue-700 space-y-1">
          <p>• سيتم إنشاء توقيع إلكتروني باستخدام HASH-SHA256</p>
          <p>• التوقيع محمي بكلمة مرور لضمان الأمان</p>
          <p>• يمكن التحقق من صحة التوقيع لاحق所有情节</p>
        </div>
      </div>

      <div class="mb-4">
        <label for="signature-password" class="block text-sm font-medium text-gray-700 mb-2">
          كلمة مرور التوقيع (مطلوب)
        </label>
        <input
          id="signature-password"
          type="password"
          bind:value={signaturePassword}
          class="w-full p-3 border border-gray-300 rounded-md"
          placeholder="أدخل كلمة مرور قوية للتوقيع..."
          autocomplete="new-password"
        />
        <p class="text-xs text-gray-500 mt-1">
          ستحتاج هذه الكلمة للتحقق من التوقيع لاحق所有情节
        </p>
      </div>

      <div class="flex space-x-3 space-x-reverse">
        <button
          class="flex-1 bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-md disabled:opacity-50"
          disabled={!signaturePassword.trim() || isProcessing}
          on:click={executeElectronicSignature}
        >
          {isProcessing ? 'جاري إنشاء التوقيع...' : 'إنشاء التوقيع الإلكتروني'}
        </button>
        <button
          class="flex-1 bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-md"
          on:click={closeSignatureModal}
        >
          إلغاء
        </button>
      </div>
    </div>
  </div>
{/if}

<!-- نافذة التحقق من التوقيع -->
{#if showVerifyModal}
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
      <h3 class="text-lg font-semibold mb-4 flex items-center gap-2">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M9 12l2 2 4-4"></path>
          <circle cx="12" cy="12" r="9"></circle>
        </svg>
        التحقق من التوقيع الإلكتروني
      </h3>

      <div class="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-md">
        <h4 class="font-semibold text-blue-800 mb-2">معلومات التحقق</h4>
        <div class="text-sm text-blue-700 space-y-1">
          <p>• هذا التوقيع محمي بكلمة مرور</p>
          <p>• أدخل كلمة المرور للتحقق من صحة التوقيع</p>
          <p>• سيتم التحقق من التوقيع باستخدام HASH-SHA256</p>
        </div>
      </div>

      <div class="mb-4">
        <label for="verify-password" class="block text-sm font-medium text-gray-700 mb-2">
          كلمة مرور التحقق (مطلوب)
        </label>
        <input
          id="verify-password"
          type="password"
          bind:value={verifyPassword}
          class="w-full p-3 border border-gray-300 rounded-md"
          placeholder="أدخل كلمة مرور التوقيع..."
          autocomplete="off"
        />
        <p class="text-xs text-gray-500 mt-1">
          نفس كلمة المرور المستخدمة عند إنشاء التوقيع
        </p>
      </div>

      {#if verificationResult}
        <div class="mb-4 p-3 rounded-md {verificationResult.valid && verificationResult.isAuthenticated ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}">
          <p class="text-sm {verificationResult.valid && verificationResult.isAuthenticated ? 'text-green-700' : 'text-red-700'}">
            {verificationResult.reason || 'نتيجة التحقق غير واضحة'}
          </p>
        </div>
      {/if}

      <div class="flex space-x-3 space-x-reverse">
        <button
          class="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md disabled:opacity-50"
          disabled={!verifyPassword.trim()}
          on:click={verifySignatureWithPassword}
        >
          🔍 التحقق من التوقيع
        </button>
        <button
          class="flex-1 bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-md"
          on:click={closeVerifyModal}
        >
          إلغاء
        </button>
      </div>
    </div>
  </div>
{/if}

<style>
  .container {
    direction: rtl;
    text-align: right;
  }

  .document-viewer {
    padding: 20px;
    max-height: 80vh;
    overflow-y: auto;
    background: white;
    direction: rtl;
    text-align: right;
  }

  .document-content-viewer {
    padding: 20px;
    max-height: 80vh;
    overflow-y: auto;
    background: white;
    direction: rtl;
    text-align: right;
    font-family: 'Amiri', 'Times New Roman', serif;
    line-height: 1.8;
  }

  .document-content-viewer :global(div) {
    direction: rtl;
    text-align: right;
    font-family: 'Amiri', 'Times New Roman', serif;
  }

  .document-content-viewer :global(img) {
    max-width: 100%;
    height: auto;
  }

  .document-content-viewer :global(span) {
    direction: rtl;
    text-align: right;
  }
</style>
