<script lang="ts">
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import { supabase } from '$lib/supabase';
  import { goto } from '$app/navigation';

  interface SignedDocument {
    id: string;
    document_id: string;
    creator_id: string;
    signer_id: string;
    status: string;
    reference_number: string;
    signature: any;
    rejection_reason?: string;
    revision_comments?: string;
    signed_at?: string;
    created_at: string;
    updated_at: string;
    document_title?: string;
    document_content?: string;
    creator_name?: string;
    creator_email?: string;
    signer_name?: string;
    signer_email?: string;
    creator_unit_name?: string;
    signer_unit_name?: string;
  }

  let signedDocument: SignedDocument | null = null;
  let loading = true;
  let error: string | null = null;
  let currentUserId: string | null = null;
  let isProcessing = false;

  // متغيرات للإجراءات
  let showRejectModal = false;
  let showRevisionModal = false;
  let rejectionReason = '';
  let revisionComments = '';

  const statusLabels: Record<string, string> = {
    'pending_signature': 'في انتظار التوقيع',
    'signed': 'موقع',
    'rejected': 'مرفوض',
    'revision_requested': 'مطلوب تعديل'
  };

  const statusColors: Record<string, string> = {
    'pending_signature': 'bg-yellow-100 text-yellow-800',
    'signed': 'bg-green-100 text-green-800',
    'rejected': 'bg-red-100 text-red-800',
    'revision_requested': 'bg-blue-100 text-blue-800'
  };

  onMount(async () => {
    await loadSignedDocument();
  });

  async function loadSignedDocument() {
    try {
      loading = true;
      error = null;

      const documentId = $page.params.id;
      if (!documentId) {
        error = 'معرف المستند غير صحيح';
        loading = false;
        return;
      }

      // الحصول على المستخدم الحالي
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        error = 'يجب تسجيل الدخول لعرض المستند';
        loading = false;
        return;
      }

      currentUserId = user.id;

      // جلب تفاصيل المستند الموقع
      let { data, error: fetchError } = await supabase
        .from('signed_documents_with_details')
        .select('*')
        .eq('id', documentId)
        .single();

      // إذا فشل الاستعلام مع view، جرب الجدول الأساسي
      if (fetchError && fetchError.message.includes('does not exist')) {
        console.log('View لا يوجد، جرب الجدول الأساسي...');
        const { data: basicData, error: basicError } = await supabase
          .from('signed_documents')
          .select('*')
          .eq('id', documentId)
          .single();

        data = basicData;
        fetchError = basicError;
      }

      if (fetchError) {
        console.error('خطأ في جلب المستند:', fetchError);
        error = 'حدث خطأ أثناء جلب المستند: ' + fetchError.message;
        return;
      }

      if (!data) {
        error = 'المستند غير موجود';
        return;
      }

      // التحقق من صلاحية الوصول
      if (data.creator_id !== user.id && data.signer_id !== user.id) {
        error = 'ليس لديك صلاحية لعرض هذا المستند';
        return;
      }

      signedDocument = data;
      console.log('تم جلب المستند الموقع:', signedDocument);
    } catch (err: any) {
      console.error('خطأ في تحميل المستند:', err);
      error = 'حدث خطأ أثناء تحميل المستند: ' + (err.message || 'خطأ غير معروف');
    } finally {
      loading = false;
    }
  }

  async function approveAndSign() {
    if (!signedDocument || !currentUserId) return;

    try {
      isProcessing = true;

      const { error: updateError } = await supabase
        .from('signed_documents')
        .update({
          status: 'signed',
          signed_at: new Date().toISOString(),
          signature: {
            ...signedDocument.signature,
            status: 'signed',
            signedAt: new Date().toISOString(),
            signedBy: currentUserId
          }
        })
        .eq('id', signedDocument.id);

      if (updateError) {
        console.error('خطأ في التوقيع:', updateError);
        alert('حدث خطأ أثناء التوقيع: ' + updateError.message);
        return;
      }

      alert('تم توقيع المستند بنجاح!');
      await loadSignedDocument(); // إعادة تحميل البيانات
    } catch (err: any) {
      console.error('خطأ في التوقيع:', err);
      alert('حدث خطأ أثناء التوقيع: ' + (err.message || 'خطأ غير معروف'));
    } finally {
      isProcessing = false;
    }
  }

  async function rejectDocument() {
    if (!signedDocument || !rejectionReason.trim()) return;

    try {
      isProcessing = true;

      // التحقق من وجود العمود أولاً
      console.log('محاولة رفض المستند مع السبب:', rejectionReason);

      const updateData: any = {
        status: 'rejected'
      };

      // إضافة rejection_reason فقط إذا كان متوفراً
      try {
        updateData.rejection_reason = rejectionReason;
      } catch (e) {
        console.warn('لا يمكن إضافة rejection_reason:', e);
      }

      // تحديث signature
      if (signedDocument.signature) {
        updateData.signature = {
          ...signedDocument.signature,
          status: 'rejected',
          rejectedAt: new Date().toISOString(),
          rejectedBy: currentUserId,
          rejectionReason: rejectionReason
        };
      }

      const { error: updateError } = await supabase
        .from('signed_documents')
        .update(updateData)
        .eq('id', signedDocument.id);

      if (updateError) {
        console.error('خطأ في الرفض:', updateError);

        // إذا كانت المشكلة في العمود، جرب بدون rejection_reason
        if (updateError.message.includes('rejection_reason')) {
          console.log('محاولة الرفض بدون حفظ السبب...');
          const { error: retryError } = await supabase
            .from('signed_documents')
            .update({
              status: 'rejected',
              signature: {
                ...signedDocument.signature,
                status: 'rejected',
                rejectedAt: new Date().toISOString(),
                rejectedBy: currentUserId,
                rejectionReason: rejectionReason
              }
            })
            .eq('id', signedDocument.id);

          if (retryError) {
            alert('حدث خطأ أثناء رفض المستند: ' + retryError.message);
            return;
          }
        } else {
          alert('حدث خطأ أثناء رفض المستند: ' + updateError.message);
          return;
        }
      }

      alert('تم رفض المستند بنجاح!');
      showRejectModal = false;
      rejectionReason = '';
      await loadSignedDocument(); // إعادة تحميل البيانات
    } catch (err: any) {
      console.error('خطأ في الرفض:', err);
      alert('حدث خطأ أثناء رفض المستند: ' + (err.message || 'خطأ غير معروف'));
    } finally {
      isProcessing = false;
    }
  }

  async function requestRevision() {
    if (!signedDocument || !revisionComments.trim()) return;

    try {
      isProcessing = true;

      console.log('محاولة طلب التعديل مع التعليقات:', revisionComments);

      const updateData: any = {
        status: 'revision_requested'
      };

      // إضافة revision_comments فقط إذا كان متوفراً
      try {
        updateData.revision_comments = revisionComments;
      } catch (e) {
        console.warn('لا يمكن إضافة revision_comments:', e);
      }

      // تحديث signature
      if (signedDocument.signature) {
        updateData.signature = {
          ...signedDocument.signature,
          status: 'revision_requested',
          revisionRequestedAt: new Date().toISOString(),
          revisionRequestedBy: currentUserId,
          revisionComments: revisionComments
        };
      }

      const { error: updateError } = await supabase
        .from('signed_documents')
        .update(updateData)
        .eq('id', signedDocument.id);

      if (updateError) {
        console.error('خطأ في طلب التعديل:', updateError);

        // إذا كانت المشكلة في العمود، جرب بدون revision_comments
        if (updateError.message.includes('revision_comments')) {
          console.log('محاولة طلب التعديل بدون حفظ التعليقات...');
          const { error: retryError } = await supabase
            .from('signed_documents')
            .update({
              status: 'revision_requested',
              signature: {
                ...signedDocument.signature,
                status: 'revision_requested',
                revisionRequestedAt: new Date().toISOString(),
                revisionRequestedBy: currentUserId,
                revisionComments: revisionComments
              }
            })
            .eq('id', signedDocument.id);

          if (retryError) {
            alert('حدث خطأ أثناء طلب التعديل: ' + retryError.message);
            return;
          }
        } else {
          alert('حدث خطأ أثناء طلب التعديل: ' + updateError.message);
          return;
        }
      }

      alert('تم طلب التعديل بنجاح!');
      showRevisionModal = false;
      revisionComments = '';
      await loadSignedDocument(); // إعادة تحميل البيانات
    } catch (err: any) {
      console.error('خطأ في طلب التعديل:', err);
      alert('حدث خطأ أثناء طلب التعديل: ' + (err.message || 'خطأ غير معروف'));
    } finally {
      isProcessing = false;
    }
  }

  function formatDate(dateString: string) {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  function getMyRole(): string {
    if (!signedDocument || !currentUserId) return 'غير محدد';
    if (signedDocument.creator_id === currentUserId) return 'منشئ';
    if (signedDocument.signer_id === currentUserId) return 'موقع';
    return 'غير محدد';
  }

  function canTakeAction(): boolean {
    if (!signedDocument || !currentUserId) return false;
    return signedDocument.signer_id === currentUserId && signedDocument.status === 'pending_signature';
  }
</script>

<svelte:head>
  <title>تفاصيل المستند الموقع</title>
</svelte:head>

<div class="container mx-auto p-6 rtl">
  <div class="bg-white rounded-lg shadow-md">
    <!-- العنوان والتنقل -->
    <div class="border-b border-gray-200 p-6">
      <div class="flex justify-between items-center mb-4">
        <h1 class="text-2xl font-bold text-gray-900">تفاصيل المستند الموقع</h1>
        <button
          class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md"
          on:click={() => goto('/dashboard/signed-documents')}
        >
          العودة للقائمة
        </button>
      </div>
    </div>

    <!-- المحتوى -->
    <div class="p-6">
      {#if loading}
        <div class="flex justify-center items-center py-12">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          <span class="mr-3 text-gray-600">جاري تحميل المستند...</span>
        </div>
      {:else if error}
        <div class="bg-red-50 border border-red-200 rounded-md p-4">
          <div class="flex">
            <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
            </svg>
            <div class="mr-3">
              <h3 class="text-sm font-medium text-red-800">خطأ</h3>
              <p class="text-sm text-red-700 mt-1">{error}</p>
            </div>
          </div>
        </div>
      {:else if signedDocument}
        <!-- معلومات المستند -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
          <!-- المعلومات الأساسية -->
          <div class="lg:col-span-2">
            <div class="bg-gray-50 rounded-lg p-6">
              <h2 class="text-xl font-semibold mb-4">معلومات المستند</h2>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p class="text-sm text-gray-600">عنوان المستند</p>
                  <p class="font-medium">{signedDocument.document_title || 'غير محدد'}</p>
                </div>
                <div>
                  <p class="text-sm text-gray-600">الرقم الإشاري</p>
                  <p class="font-medium">{signedDocument.reference_number}</p>
                </div>
                <div>
                  <p class="text-sm text-gray-600">المنشئ</p>
                  <p class="font-medium">{signedDocument.creator_name || signedDocument.creator_id || 'غير محدد'}</p>
                </div>
                <div>
                  <p class="text-sm text-gray-600">الموقع المطلوب</p>
                  <p class="font-medium">{signedDocument.signer_name || signedDocument.signer_id || 'غير محدد'}</p>
                </div>
                <div>
                  <p class="text-sm text-gray-600">تاريخ الإنشاء</p>
                  <p class="font-medium">{formatDate(signedDocument.created_at)}</p>
                </div>
                <div>
                  <p class="text-sm text-gray-600">دوري في هذا المستند</p>
                  <p class="font-medium">{getMyRole()}</p>
                </div>
              </div>
            </div>
          </div>

          <!-- حالة المستند والإجراءات -->
          <div>
            <div class="bg-gray-50 rounded-lg p-6">
              <h2 class="text-xl font-semibold mb-4">حالة المستند</h2>
              <div class="space-y-4">
                <div>
                  <span class="px-3 py-1 text-sm font-medium rounded-full {statusColors[signedDocument.status]}">
                    {statusLabels[signedDocument.status]}
                  </span>
                </div>

                {#if signedDocument.signed_at}
                  <div>
                    <p class="text-sm text-gray-600">تاريخ التوقيع</p>
                    <p class="font-medium">{formatDate(signedDocument.signed_at)}</p>
                  </div>
                {/if}

                {#if canTakeAction()}
                  <div class="space-y-2">
                    <button
                      class="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-md disabled:opacity-50"
                      disabled={isProcessing}
                      on:click={approveAndSign}
                    >
                      {isProcessing ? 'جاري التوقيع...' : 'الموافقة والتوقيع'}
                    </button>
                    <button
                      class="w-full bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-md"
                      on:click={() => showRejectModal = true}
                    >
                      رفض المستند
                    </button>
                    <button
                      class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md"
                      on:click={() => showRevisionModal = true}
                    >
                      طلب تعديل
                    </button>
                  </div>
                {/if}
              </div>
            </div>
          </div>
        </div>

        <!-- عرض أسباب الرفض أو تعليقات التعديل -->
        {#if signedDocument.rejection_reason}
          <div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
            <h3 class="text-lg font-semibold text-red-800 mb-2">سبب الرفض</h3>
            <p class="text-red-700">{signedDocument.rejection_reason}</p>
          </div>
        {/if}

        {#if signedDocument.revision_comments}
          <div class="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
            <h3 class="text-lg font-semibold text-blue-800 mb-2">تعليقات التعديل</h3>
            <p class="text-blue-700">{signedDocument.revision_comments}</p>
          </div>
        {/if}

        <!-- روابط إضافية -->
        <div class="mb-6 flex space-x-4 space-x-reverse">
          <button
            class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md"
            on:click={async () => {
              if (!signedDocument) return;

              // البحث عن الرسالة التي تحتوي على هذا المستند كمرفق
              const { data: messages } = await supabase
                .from('messages')
                .select('id')
                .contains('attachment', { signature_request_id: signedDocument.id })
                .limit(1);

              if (messages && messages.length > 0) {
                goto(`/dashboard/messages/view/${messages[0].id}`);
              } else {
                alert('لم يتم العثور على الرسالة المرتبطة بهذا المستند');
              }
            }}
          >
            عرض الرسالة والمرفق
          </button>
        </div>

        <!-- محتوى المستند -->
        {#if signedDocument.document_content}
          <div class="border border-gray-300 rounded-lg overflow-hidden">
            <div class="bg-gray-100 px-4 py-2 border-b border-gray-300">
              <h3 class="text-lg font-semibold">محتوى المستند</h3>
            </div>
            <div class="document-content-viewer">
              {@html signedDocument.document_content}
            </div>
          </div>
        {:else}
          <div class="border border-gray-300 rounded-lg p-6 text-center">
            <p class="text-gray-500">محتوى المستند غير متوفر. يمكنك عرض المستند الكامل من خلال الرسالة المرفقة.</p>
          </div>
        {/if}
      {/if}
    </div>
  </div>
</div>

<!-- نافذة رفض المستند -->
{#if showRejectModal}
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
      <h3 class="text-lg font-semibold mb-4">رفض المستند</h3>
      <div class="mb-4">
        <label for="rejection-reason" class="block text-sm font-medium text-gray-700 mb-2">
          سبب الرفض (مطلوب)
        </label>
        <textarea
          id="rejection-reason"
          bind:value={rejectionReason}
          class="w-full p-3 border border-gray-300 rounded-md"
          rows="4"
          placeholder="يرجى ذكر سبب رفض المستند..."
        ></textarea>
      </div>
      <div class="flex space-x-3 space-x-reverse">
        <button
          class="flex-1 bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-md disabled:opacity-50"
          disabled={!rejectionReason.trim() || isProcessing}
          on:click={rejectDocument}
        >
          {isProcessing ? 'جاري الرفض...' : 'رفض المستند'}
        </button>
        <button
          class="flex-1 bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-md"
          on:click={() => {
            showRejectModal = false;
            rejectionReason = '';
          }}
        >
          إلغاء
        </button>
      </div>
    </div>
  </div>
{/if}

<!-- نافذة طلب التعديل -->
{#if showRevisionModal}
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
      <h3 class="text-lg font-semibold mb-4">طلب تعديل</h3>
      <div class="mb-4">
        <label for="revision-comments" class="block text-sm font-medium text-gray-700 mb-2">
          تعليقات التعديل (مطلوب)
        </label>
        <textarea
          id="revision-comments"
          bind:value={revisionComments}
          class="w-full p-3 border border-gray-300 rounded-md"
          rows="4"
          placeholder="يرجى ذكر التعديلات المطلوبة..."
        ></textarea>
      </div>
      <div class="flex space-x-3 space-x-reverse">
        <button
          class="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md disabled:opacity-50"
          disabled={!revisionComments.trim() || isProcessing}
          on:click={requestRevision}
        >
          {isProcessing ? 'جاري الطلب...' : 'طلب التعديل'}
        </button>
        <button
          class="flex-1 bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-md"
          on:click={() => {
            showRevisionModal = false;
            revisionComments = '';
          }}
        >
          إلغاء
        </button>
      </div>
    </div>
  </div>
{/if}

<style>
  .container {
    direction: rtl;
    text-align: right;
  }

  .document-content-viewer {
    padding: 20px;
    max-height: 80vh;
    overflow-y: auto;
    background: white;
    direction: rtl;
    text-align: right;
    font-family: 'Amiri', 'Times New Roman', serif;
    line-height: 1.8;
  }

  .document-content-viewer :global(div) {
    direction: rtl;
    text-align: right;
    font-family: 'Amiri', 'Times New Roman', serif;
  }

  .document-content-viewer :global(img) {
    max-width: 100%;
    height: auto;
  }

  .document-content-viewer :global(span) {
    direction: rtl;
    text-align: right;
  }
</style>
