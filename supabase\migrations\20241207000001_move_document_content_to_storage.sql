-- نقل محتوى المستندات إلى Supabase Storage لتحسين الأداء
-- هذا الملف يضيف عمود content_url ويحدث النظام لاستخدام التخزين السحابي

-- 1. التأكد من وجود bucket saadabujnah
INSERT INTO storage.buckets (id, name, public)
VALUES ('saadabujnah', 'Saadabujnah Documents', false)
ON CONFLICT (id) DO NOTHING;

-- 2. إضافة سياسات الأمان للـ bucket
-- سياسة القراءة: المستخدمون المصرح لهم فقط
CREATE POLICY IF NOT EXISTS "Authenticated users can read documents"
ON storage.objects
FOR SELECT
USING (
  bucket_id = 'saadabujnah' 
  AND auth.role() = 'authenticated'
);

-- سياسة الكتابة: المستخدمون المصرح لهم فقط
CREATE POLICY IF NOT EXISTS "Authenticated users can upload documents"
ON storage.objects
FOR INSERT
WITH CHECK (
  bucket_id = 'saadabujnah' 
  AND auth.role() = 'authenticated'
);

-- سياسة التحديث: المستخدمون المصرح لهم فقط
CREATE POLICY IF NOT EXISTS "Authenticated users can update documents"
ON storage.objects
FOR UPDATE
USING (
  bucket_id = 'saadabujnah' 
  AND auth.role() = 'authenticated'
);

-- سياسة الحذف: المستخدمون المصرح لهم فقط
CREATE POLICY IF NOT EXISTS "Authenticated users can delete documents"
ON storage.objects
FOR DELETE
USING (
  bucket_id = 'saadabujnah' 
  AND auth.role() = 'authenticated'
);

-- 3. إضافة عمود content_url إلى جدول documents
ALTER TABLE public.documents 
ADD COLUMN IF NOT EXISTS content_url TEXT;

-- 4. إضافة عمود content_url إلى جدول messages للمرفقات
ALTER TABLE public.messages 
ADD COLUMN IF NOT EXISTS attachment_content_url TEXT;

-- 5. إنشاء فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_documents_content_url ON public.documents(content_url) WHERE content_url IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_messages_attachment_content_url ON public.messages(attachment_content_url) WHERE attachment_content_url IS NOT NULL;

-- 6. إنشاء وظيفة لتوليد مسار فريد للملف
CREATE OR REPLACE FUNCTION public.generate_document_storage_path(
  document_id UUID,
  file_type TEXT DEFAULT 'html'
)
RETURNS TEXT AS $$
BEGIN
  RETURN 'documents/' || 
         EXTRACT(YEAR FROM NOW())::TEXT || '/' ||
         EXTRACT(MONTH FROM NOW())::TEXT || '/' ||
         document_id::TEXT || '.' || file_type;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 7. إنشاء وظيفة لتوليد مسار فريد للمرفقات
CREATE OR REPLACE FUNCTION public.generate_attachment_storage_path(
  message_id UUID,
  file_type TEXT DEFAULT 'html'
)
RETURNS TEXT AS $$
BEGIN
  RETURN 'attachments/' || 
         EXTRACT(YEAR FROM NOW())::TEXT || '/' ||
         EXTRACT(MONTH FROM NOW())::TEXT || '/' ||
         message_id::TEXT || '.' || file_type;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 8. إنشاء وظيفة لحفظ محتوى المستند في Storage
CREATE OR REPLACE FUNCTION public.save_document_content_to_storage(
  document_id UUID,
  content TEXT
)
RETURNS TEXT AS $$
DECLARE
  storage_path TEXT;
  upload_result JSONB;
BEGIN
  -- توليد مسار التخزين
  storage_path := public.generate_document_storage_path(document_id, 'html');
  
  -- محاولة حفظ المحتوى في Storage
  -- ملاحظة: هذا يتطلب استخدام JavaScript/TypeScript في الواجهة الأمامية
  -- هذه الوظيفة تعيد المسار المطلوب فقط
  
  RETURN storage_path;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 9. إنشاء وظيفة لحفظ محتوى المرفق في Storage
CREATE OR REPLACE FUNCTION public.save_attachment_content_to_storage(
  message_id UUID,
  content TEXT
)
RETURNS TEXT AS $$
DECLARE
  storage_path TEXT;
BEGIN
  -- توليد مسار التخزين
  storage_path := public.generate_attachment_storage_path(message_id, 'html');
  
  RETURN storage_path;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 10. إنشاء وظيفة لجلب محتوى المستند من Storage
CREATE OR REPLACE FUNCTION public.get_document_content_from_storage(
  content_url TEXT
)
RETURNS TEXT AS $$
BEGIN
  -- هذه الوظيفة ستستخدم في الواجهة الأمامية
  -- تعيد URL للوصول للمحتوى
  RETURN content_url;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 11. تحديث تعليقات الجداول
COMMENT ON COLUMN public.documents.content IS 'محتوى المستند (مهجور - استخدم content_url)';
COMMENT ON COLUMN public.documents.content_url IS 'رابط محتوى المستند في Supabase Storage';
COMMENT ON COLUMN public.messages.attachment_content_url IS 'رابط محتوى المرفق في Supabase Storage';

-- 12. إنشاء view لعرض المستندات مع المحتوى
CREATE OR REPLACE VIEW public.documents_with_content AS
SELECT 
  d.*,
  CASE 
    WHEN d.content_url IS NOT NULL THEN d.content_url
    ELSE NULL
  END as storage_content_url,
  CASE 
    WHEN d.content_url IS NOT NULL THEN 'storage'
    WHEN d.content IS NOT NULL THEN 'database'
    ELSE 'none'
  END as content_source
FROM public.documents d;

-- 13. إنشاء view لعرض الرسائل مع المرفقات
CREATE OR REPLACE VIEW public.messages_with_attachments AS
SELECT 
  m.*,
  CASE 
    WHEN m.attachment_content_url IS NOT NULL THEN m.attachment_content_url
    ELSE NULL
  END as storage_attachment_url,
  CASE 
    WHEN m.attachment_content_url IS NOT NULL THEN 'storage'
    WHEN m.attachment IS NOT NULL THEN 'database'
    ELSE 'none'
  END as attachment_source
FROM public.messages m;

-- 14. تحديث ذاكرة التخزين المؤقت
NOTIFY pgrst, 'reload schema';
