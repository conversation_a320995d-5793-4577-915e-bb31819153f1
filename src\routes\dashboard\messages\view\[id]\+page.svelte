<script lang="ts">
  import { supabase } from '$lib/supabase';
  import { onMount } from 'svelte';
  import SignatureBadge from '$lib/components/SignatureBadge.svelte';
  import OfficialDocument from '$lib/components/OfficialDocument.svelte';

  export let data: { params: { id: string } };

  interface Sender {
    full_name: string;
    email: string;
  }

  interface Recipient {
    full_name: string;
    email: string;
  }

  interface Unit {
    name: string;
  }

  interface Reply {
    id: string;
    subject: string;
    content: string;
    sender_id: string;
    created_at: string;
    sender?: Sender;
  }

  interface Message {
    id: string;
    subject: string;
    content: string;
    sender_id: string;
    recipient_id: string;
    receiver_id: string | null;
    receiver_unit_id: string | null;
    is_read: boolean;
    created_at: string;
    updated_at: string;
    is_signed: boolean;
    signature: any;
    is_official?: boolean;
    unit_name?: string;
    unit_logo?: string | null;
    organization_logo?: string | null;
    stamp?: string | null;
    logo_text?: string | null;
    attachment?: any;
    sender?: Sender;
    recipient?: Recipient;
    unit?: Unit;
    replies?: Reply[];
  }

  // استخدام معرف الرسالة من البيانات
  const messageId = data.params.id;
  let message: Message | null = null;
  let loading = true;
  let error: string | null = null;
  let currentUserId = '';
  let replyContent = '';
  let showReplyForm = false;

  // حالة التوقيع
  let isValid = false;
  let isRecipient = false;
  let isAuthenticated = false;
  let showSignatureDetails = true;

  onMount(async () => {
    try {
      // جلب بيانات المستخدم الحالي
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        currentUserId = user.id;
      }

      // جلب بيانات الرسالة
      await fetchMessage();
    } catch (err) {
      console.error('Error in onMount:', err);
      error = 'حدث خطأ أثناء تحميل البيانات';
    }
  });

  async function fetchMessage() {
    try {
      loading = true;
      error = null;

      // جلب بيانات الرسالة
      const { data: messageData, error: messageError } = await supabase
        .from('messages')
        .select('*')
        .eq('id', messageId)
        .single();

      if (messageError) {
        console.error('Error fetching message:', messageError);
        throw new Error('فشل في جلب بيانات الرسالة');
      }

      if (!messageData) {
        throw new Error('لم يتم العثور على الرسالة');
      }

      // تحويل البيانات إلى كائن Message
      const fetchedMessage: Message = {
        id: messageData.id,
        subject: messageData.subject,
        content: messageData.content,
        sender_id: messageData.sender_id,
        recipient_id: messageData.recipient_id,
        receiver_id: messageData.receiver_id,
        receiver_unit_id: messageData.receiver_unit_id,
        is_read: messageData.is_read,
        created_at: messageData.created_at,
        updated_at: messageData.updated_at,
        is_signed: messageData.is_signed || false,
        signature: messageData.signature || null,
        is_official: messageData.is_official || false,
        unit_name: messageData.unit_name || '',
        unit_logo: messageData.unit_logo || null,
        organization_logo: messageData.organization_logo || null,
        stamp: messageData.stamp || null,
        logo_text: null, // سيتم تعيينه لاحقاً من إعدادات المنظمة
        attachment: messageData.attachment || null
      };

      message = fetchedMessage;
      console.log('تم جلب بيانات الرسالة:', message);

      // جلب بيانات المرسل
      if (message.sender_id) {
        const { data: senderData } = await supabase
          .from('profiles')
          .select('full_name, email')
          .eq('id', message.sender_id)
          .single();

        if (senderData) {
          message.sender = senderData;
        }
      }

      // جلب بيانات المستقبل (مستخدم)
      if (message.recipient_id) {
        const { data: recipientData } = await supabase
          .from('profiles')
          .select('full_name, email')
          .eq('id', message.recipient_id)
          .single();

        if (recipientData) {
          message.recipient = recipientData;
        }
      }

      // جلب بيانات المستقبل (وحدة تنظيمية)
      if (message.receiver_unit_id) {
        const { data: unitData } = await supabase
          .from('units')
          .select('name')
          .eq('id', message.receiver_unit_id)
          .single();

        if (unitData) {
          message.unit = unitData;
        }
      }

      // تحديث حالة القراءة
      if (
        message.recipient_id === currentUserId ||
        message.receiver_id === currentUserId
      ) {
        await supabase
          .from('messages')
          .update({ is_read: true })
          .eq('id', messageId);

        message.is_read = true;
      }

      // جلب الردود
      const { data: repliesData } = await supabase
        .from('messages')
        .select(`
          *,
          sender:sender_id(full_name, email)
        `)
        .eq('parent_id', messageId)
        .order('created_at', { ascending: true });

      if (repliesData) {
        message.replies = repliesData;
      }

      // جلب نص الشعار من إعدادات المنظمة
      try {
        console.log('جلب نص الشعار من إعدادات المنظمة...');
        const { data: orgSettings, error: orgError } = await supabase
          .from('organization_settings')
          .select('logo')
          .single();

        if (orgError) {
          console.error('خطأ في جلب إعدادات المنظمة:', orgError);
        } else if (orgSettings && orgSettings.logo) {
          // تعيين نص الشعار مباشرة من إعدادات المنظمة
          message.logo_text = orgSettings.logo;
          console.log('تم جلب نص الشعار من إعدادات المنظمة:', message.logo_text);
        } else {
          console.log('لم يتم العثور على نص الشعار في إعدادات المنظمة');
        }
      } catch (orgError) {
        console.error('استثناء في جلب إعدادات المنظمة:', orgError);
      }

      console.log('القيم النهائية للرسالة:', {
        unit_name: message.unit_name,
        logo_text: message.logo_text,
        unit: message.unit
      });
    } catch (err: any) {
      console.error('Error fetching message details:', err);
      error = err.message || 'حدث خطأ أثناء جلب بيانات الرسالة';
    } finally {
      loading = false;
    }
  }

  async function handleSendReply() {
    if (!replyContent.trim()) {
      alert('يرجى إدخال محتوى الرد');
      return;
    }

    if (!message) {
      alert('لم يتم العثور على الرسالة');
      return;
    }

    try {
      // إظهار حالة التحميل
      const sendButton = document.querySelector('#send-reply-button');
      if (sendButton) {
        sendButton.setAttribute('disabled', 'true');
        sendButton.innerHTML = `
          <svg class="animate-spin ml-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          جاري الإرسال...
        `;
      }

      // تحقق من وجود عمود parent_id في جدول messages
      try {
        // محاولة إضافة عمود parent_id إذا لم يكن موجوداً
        await supabase.rpc('add_parent_id_column_if_not_exists');
      } catch (columnError) {
        console.log('تم تجاهل خطأ إضافة العمود:', columnError);
        // تجاهل الخطأ والمتابعة
      }

      // إنشاء بيانات الرد
      const replyData: any = {
        subject: `رد: ${message.subject}`,
        content: replyContent,
        sender_id: currentUserId,
        recipient_id: message.sender_id,
        receiver_id: message.sender_id,
        is_read: false
      };

      // إضافة parent_id فقط إذا كان العمود موجوداً
      try {
        // التحقق من وجود العمود
        const { data: columnExists } = await supabase.rpc('check_column_exists', {
          table_name: 'messages',
          column_name: 'parent_id'
        });

        if (columnExists) {
          replyData.parent_id = messageId;
        }
      } catch (checkError) {
        console.log('تم تجاهل خطأ التحقق من وجود العمود:', checkError);
        // تجاهل الخطأ والمتابعة بدون إضافة parent_id
      }

      const { error: replyError } = await supabase
        .from('messages')
        .insert([replyData])
        .select();

      if (replyError) {
        throw replyError;
      }

      // إعادة تحميل الرسالة والردود
      await fetchMessage();

      // إعادة تعيين نموذج الرد
      replyContent = '';
      showReplyForm = false;
    } catch (err: any) {
      console.error('Error sending reply:', err);
      alert('حدث خطأ أثناء إرسال الرد: ' + (err.message || 'خطأ غير معروف'));
    } finally {
      // إعادة تعيين حالة الزر
      const sendButton = document.querySelector('#send-reply-button');
      if (sendButton) {
        sendButton.removeAttribute('disabled');
        sendButton.innerHTML = `
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-2"><path d="m22 2-7 20-4-9-9-4Z"/><path d="M22 2 11 13"/></svg>
          إرسال الرد
        `;
      }
    }
  }

  function formatDate(dateString: string): string {
    if (!dateString) return '';
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric'
    }).format(date);
  }
</script>

<div class="container mx-auto p-4">
  <div class="mb-6 flex justify-between items-center">
    <a
      href="/dashboard/messages"
      class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2"
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-2"><path d="m15 18-6-6 6-6"/></svg>
      العودة إلى المراسلات
    </a>

    <h1 class="text-xl font-bold text-primary">عرض الرسالة</h1>
  </div>

  {#if loading}
    <div class="flex justify-center items-center h-64">
      <p class="text-lg">جاري التحميل...</p>
    </div>
  {:else if error}
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
      <p>{error}</p>
    </div>
  {:else if message}
    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
      <!-- رأس الرسالة -->
      <div class="p-6 border-b bg-gray-50">
        <h1 class="text-2xl font-bold mb-4 text-primary">{message.subject}</h1>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm bg-white p-4 rounded-lg shadow-sm">
          <div class="flex items-center">
            <span class="font-semibold ml-2 text-gray-700">من:</span>
            <span class="text-primary font-medium">{message.sender?.full_name || 'غير معروف'}</span>
          </div>

          <div class="flex items-center">
            <span class="font-semibold ml-2 text-gray-700">إلى:</span>
            <span class="text-primary font-medium">{message.recipient?.full_name || message.unit?.name || 'غير معروف'}</span>
          </div>

          <div class="flex items-center">
            <span class="font-semibold ml-2 text-gray-700">التاريخ:</span>
            <span>{formatDate(message.created_at)}</span>
          </div>

          <div class="flex items-center">
            <span class="font-semibold ml-2 text-gray-700">الحالة:</span>
            <span class="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-semibold {message.is_read ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'}">
              {message.is_read ? 'تمت القراءة' : 'لم تتم القراءة'}
            </span>
          </div>
        </div>
      </div>

      <!-- محتوى الرسالة -->
      <div class="p-6 border-b">
        {#if message.is_signed}
          <div class="mb-4">
            <SignatureBadge
              signature={message.signature}
              content={message.content}
              subject={message.subject}
              showDetails={showSignatureDetails}
              messageId={message.id}
              on:verified={(event) => {
                isValid = event.detail.isValid;
                isRecipient = event.detail.isRecipient;
                isAuthenticated = event.detail.isAuthenticated;
              }}
            />
          </div>
        {/if}

        {#if message.attachment && message.attachment.type === 'document'}
          <!-- عرض المستند المرفق -->
          <div class="attachment-container mb-6">
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
              <div class="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <div>
                  <h3 class="text-lg font-semibold text-blue-800">مستند مرفق</h3>
                  <p class="text-sm text-blue-600">
                    {message.attachment.title} - الرقم الإشاري: {message.attachment.reference_number}
                  </p>
                </div>
              </div>
            </div>

            <!-- عرض المستند الكامل -->
            <div class="document-attachment border border-gray-300 rounded-lg overflow-hidden">
              {@html message.attachment.content}
            </div>

            <!-- أزرار التفاعل مع المستند -->
            <div class="mt-4 flex justify-center space-x-4 space-x-reverse">
              <button
                class="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded flex items-center"
                on:click={() => {
                  // طباعة المستند المرفق
                  const printWindow = window.open('', '_blank');
                  if (printWindow && message?.attachment) {
                    printWindow.document.write(`
                      <!DOCTYPE html>
                      <html dir="rtl" lang="ar">
                      <head>
                        <meta charset="utf-8">
                        <title>${message.attachment.title}</title>
                        <style>
                          @media print {
                            body { margin: 0; padding: 0; }
                          }
                          @page { size: 210mm 297mm; margin: 0; }
                          body { font-family: 'Amiri', 'Times New Roman', serif; }
                        </style>
                      </head>
                      <body>
                        ${message.attachment.content}
                      </body>
                      </html>
                    `);
                    printWindow.document.close();
                    setTimeout(() => {
                      printWindow.print();
                      printWindow.close();
                    }, 500);
                  }
                }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-2">
                  <polyline points="6,9 6,2 18,2 18,9"></polyline>
                  <path d="M6,18H4a2,2,0,0,1-2-2V11a2,2,0,0,1,2-2H20a2,2,0,0,1,2,2v5a2,2,0,0,1-2,2H18"></path>
                  <polyline points="6,14 18,14 18,18 6,18"></polyline>
                </svg>
                طباعة المستند
              </button>

              {#if message.attachment.signature_request_id}
                <button
                  class="bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded flex items-center"
                  on:click={() => {
                    window.location.href = `/dashboard/signed-documents/${message?.attachment?.signature_request_id}`;
                  }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-2">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                    <polyline points="14,2 14,8 20,8"></polyline>
                    <line x1="16" y1="13" x2="8" y2="13"></line>
                    <line x1="16" y1="17" x2="8" y2="17"></line>
                    <polyline points="10,9 9,9 8,9"></polyline>
                  </svg>
                  إدارة التوقيع
                </button>
              {/if}
            </div>
          </div>
        {:else if message.is_official && (!message.is_signed || (message.is_signed && (isAuthenticated || (!message.signature?.passwordProtected && isValid))))}
          <!-- عرض الوثيقة الرسمية إذا كانت رسمية وتم التحقق من التوقيع -->
          <div class="official-document-container">
            <OfficialDocument
              subject={message.subject}
              content={message.content}
              unitName={message.unit_name}
              logoText={message.logo_text || ''}
              stateName="دولة ليبيا"
              unitLogo={message.unit_logo}
              organizationLogo={message.organization_logo}
              stamp={message.stamp}
              signature={message.signature}
              sender={message.sender}
              printMode={false}
              referenceNumber={message.signature?.signatureId?.substring(0, 8) || ''}
              date={new Date(message.created_at).toISOString().split('T')[0]}
            />

            <div class="mt-4 flex justify-center">
              <button
                class="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded flex items-center"
                on:click={() => {
                  // طباعة الوثيقة باستخدام طريقة أكثر حداثة
                  const printWindow = window.open('', '_blank');
                  if (printWindow && message) {
                    // إنشاء عناصر HTML بدلاً من استخدام document.write
                    const docType = printWindow.document.implementation.createDocumentType('html', '', '');
                    printWindow.document.implementation.createDocument('', 'html', docType);

                    // تعيين اتجاه النص واللغة
                    printWindow.document.documentElement.setAttribute('dir', 'rtl');
                    printWindow.document.documentElement.setAttribute('lang', 'ar');

                    // إنشاء عنصر head
                    const head = printWindow.document.createElement('head');

                    // إضافة meta charset
                    const meta = printWindow.document.createElement('meta');
                    meta.setAttribute('charset', 'utf-8');
                    head.appendChild(meta);

                    // إضافة عنوان الصفحة
                    const title = printWindow.document.createElement('title');
                    title.textContent = message.subject;
                    head.appendChild(title);

                    // إضافة تنسيقات CSS
                    const style = printWindow.document.createElement('style');
                    style.textContent = `
                      @media print {
                        body {
                          margin: 0;
                          padding: 0;
                        }

                        /* تنسيقات الطباعة تم نقلها إلى مكون OfficialDocument.svelte */
                      }

                      @page {
                        size: 210mm 297mm; /* تحديد مقاس A4 بالضبط */
                        margin: 0;
                      }
                    `;
                    head.appendChild(style);

                    // إضافة head إلى الوثيقة
                    printWindow.document.documentElement.appendChild(head);

                    // إنشاء عنصر body
                    const body = printWindow.document.createElement('body');

                    // إنشاء حاوية للمحتوى
                    const printContent = printWindow.document.createElement('div');
                    printContent.id = 'printContent';
                    body.appendChild(printContent);

                    // إضافة body إلى الوثيقة
                    printWindow.document.documentElement.appendChild(body);

                    // نسخ محتوى الوثيقة إلى النافذة الجديدة
                    const officialDoc = document.querySelector('.official-document-container .official-document');
                    if (officialDoc) {
                      const clonedDoc = officialDoc.cloneNode(true);
                      printContent.appendChild(clonedDoc);

                      // طباعة الوثيقة
                      setTimeout(() => {
                        printWindow.print();
                        printWindow.close();
                      }, 500);
                    }
                  }
                }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-2"><polyline points="6 9 6 2 18 2 18 9"></polyline><path d="M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2"></path><rect width="12" height="8" x="6" y="14"></rect></svg>
                طباعة الوثيقة
              </button>
            </div>

            {#if message.is_signed && message.signature && message.signature.passwordProtected && isAuthenticated}
              <div class="mt-4 pt-4 border-t border-gray-200">
                <div class="text-sm text-green-600 flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-1"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><polyline points="22 4 12 14.01 9 11.01"></polyline></svg>
                  <span>تم التحقق من هويتك وفتح الوثيقة الرسمية بنجاح</span>
                </div>
              </div>
            {/if}
          </div>
        {:else if !message.is_official && (!message.is_signed || (message.is_signed && (isAuthenticated || (!message.signature?.passwordProtected && isValid))))}
          <!-- عرض محتوى الرسالة العادية إذا كانت غير رسمية وتم التحقق من التوقيع -->
          <div class="whitespace-pre-wrap bg-white rounded-lg p-6 shadow-sm border border-gray-100">
            {message.content}

            {#if message.is_signed && message.signature && message.signature.passwordProtected && isAuthenticated}
              <div class="mt-4 pt-4 border-t border-gray-200">
                <div class="text-sm text-green-600 flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-1"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><polyline points="22 4 12 14.01 9 11.01"></polyline></svg>
                  <span>تم التحقق من هويتك وفتح الرسالة بنجاح</span>
                </div>
              </div>
            {/if}
          </div>
        {:else}
          <!-- عرض رسالة مقفلة إذا كانت الرسالة موقعة ولم يتم التحقق من التوقيع -->
          <div class="bg-gray-50 rounded-lg p-6 shadow-sm border border-gray-200 text-center">
            <div class="flex flex-col items-center justify-center py-8">
              <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-gray-400 mb-4"><rect width="18" height="11" x="3" y="11" rx="2" ry="2"/><path d="M7 11V7a5 5 0 0 1 10 0v4"/></svg>
              <h3 class="text-lg font-medium text-gray-900 mb-2">محتوى {message.is_official ? 'الوثيقة' : 'الرسالة'} مقفل</h3>
              <p class="text-gray-600 mb-4 max-w-md">
                {#if isRecipient}
                  أنت المستلم المقصود لهذه {message.is_official ? 'الوثيقة' : 'الرسالة'}. يرجى إدخال كلمة المرور للتحقق من هويتك وفتح المحتوى.
                {:else}
                  هذه {message.is_official ? 'الوثيقة' : 'الرسالة'} محمية بتوقيع إلكتروني ولا يمكن عرض محتواها إلا للمستلم المقصود بعد التحقق من هويته.
                {/if}
              </p>
            </div>
          </div>
        {/if}
      </div>

      <!-- نموذج الرد -->
      {#if message.sender_id !== currentUserId}
        {#if !showReplyForm}
          <div class="p-4 border-b flex justify-end">
            <button
              class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
              on:click={() => showReplyForm = true}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-2"><path d="m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z"/></svg>
              الرد على الرسالة
            </button>
          </div>
        {:else}
          <div class="p-6 border-b bg-gray-50">
            <div class="flex justify-between items-center mb-4">
              <h3 class="font-bold text-primary text-lg">الرد على الرسالة</h3>
              <button
                class="text-gray-500 hover:text-gray-700"
                on:click={() => showReplyForm = false}
                aria-label="إغلاق نموذج الرد"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
              </button>
            </div>

            <div class="space-y-4">
              <div class="bg-[#1e293b] rounded-lg p-4 shadow-sm">
                <textarea
                  bind:value={replyContent}
                  placeholder="اكتب ردك هنا..."
                  class="w-full p-2 border-0 focus:ring-0 focus:outline-none bg-transparent min-h-[100px] resize-none text-white"
                  rows="4"
                ></textarea>
              </div>

              <div class="flex justify-end">
                <button
                  id="send-reply-button"
                  class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-6 py-2"
                  on:click={handleSendReply}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-2"><path d="m22 2-7 20-4-9-9-4Z"/><path d="M22 2 11 13"/></svg>
                  إرسال الرد
                </button>
              </div>
            </div>
          </div>
        {/if}
      {/if}

      <!-- الردود -->
      {#if message.replies && message.replies.length > 0}
        <div class="p-6">
          <h3 class="font-bold mb-4 text-primary">الردود ({message.replies.length})</h3>

          <div class="space-y-6">
            {#each message.replies as reply}
              <div class="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
                <div class="flex justify-between items-center mb-3">
                  <div class="font-medium text-primary">{reply.sender?.full_name || 'غير معروف'}</div>
                  <div class="text-sm text-gray-500">{formatDate(reply.created_at)}</div>
                </div>
                <div class="whitespace-pre-wrap bg-gray-50 p-3 rounded-md">{reply.content}</div>
              </div>
            {/each}
          </div>
        </div>
      {/if}
    </div>
  {:else}
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
      <p>لم يتم العثور على الرسالة</p>
    </div>
  {/if}
</div>

<style>
  .official-document-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 0.5rem;
  }

  .attachment-container {
    direction: rtl;
    text-align: right;
  }

  .document-attachment {
    background: white;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }

  .document-attachment :global(div) {
    direction: rtl;
    text-align: right;
    font-family: 'Amiri', 'Times New Roman', serif;
  }

  .document-attachment :global(img) {
    max-width: 100%;
    height: auto;
  }

  /* تنسيقات الطباعة تم نقلها إلى مكون OfficialDocument.svelte */
</style>