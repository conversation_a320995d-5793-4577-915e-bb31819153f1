import { supabase } from '$lib/supabase';
import { createSignature, verifySignature } from '$lib/utils/signatureUtils';

export type SignedDocumentStatus =
  | 'pending_signature'
  | 'signed'
  | 'sent'
  | 'under_review'
  | 'approved'
  | 'rejected'
  | 'returned_for_edit';

export interface SignedDocument {
  id: string;
  document_id: string;
  creator_id: string;
  signer_id: string | null;
  signed_at: string | null;
  signature: any | null;
  recipient_id: string | null;
  recipient_unit_id: string | null;
  status: SignedDocumentStatus;
  reviewer_id: string | null;
  reviewed_at: string | null;
  review_notes: string | null;
  password_protected: boolean;
  reference_number: string | null;
  created_at: string;
  updated_at: string;

  // العلاقات
  document?: any;
  creator?: any;
  signer?: any;
  recipient?: any;
  recipient_unit?: any;
  reviewer?: any;
}

export interface SignedDocumentHistory {
  id: string;
  signed_document_id: string;
  user_id: string;
  action: string;
  status: SignedDocumentStatus;
  details: any;
  created_at: string;
  user?: any;
}

export class SignedDocumentService {
  /**
   * إنشاء طلب توقيع مستند
   * @param documentId معرف المستند
   * @param creatorId معرف المنشئ
   * @param signerId معرف الموقع
   * @param passwordProtected هل المستند محمي بكلمة مرور
   */
  static async createSignatureRequest(
    documentId: string,
    creatorId: string,
    signerId: string,
    passwordProtected: boolean = false
  ): Promise<SignedDocument | null> {
    try {
      const { data, error } = await supabase
        .from('signed_documents')
        .insert({
          document_id: documentId,
          creator_id: creatorId,
          signer_id: signerId,
          status: 'pending_signature',
          password_protected: passwordProtected
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating signature request:', error);
        return null;
      }

      return data;
    } catch (err) {
      console.error('Exception in createSignatureRequest:', err);
      return null;
    }
  }

  /**
   * توقيع مستند
   * @param signedDocumentId معرف المستند الموقع
   * @param signerId معرف الموقع
   * @param documentContent محتوى المستند
   * @param documentTitle عنوان المستند
   * @param password كلمة المرور (إذا كان المستند محمي بكلمة مرور)
   */
  static async signDocument(
    signedDocumentId: string,
    signerId: string,
    documentContent: string,
    documentTitle: string,
    password: string = ''
  ): Promise<SignedDocument | null> {
    try {
      // التحقق من وجود المستند وأن الموقع هو المستخدم الحالي
      const { data: signedDoc, error: fetchError } = await supabase
        .from('signed_documents')
        .select('*')
        .eq('id', signedDocumentId)
        .eq('signer_id', signerId)
        .eq('status', 'pending_signature')
        .single();

      if (fetchError || !signedDoc) {
        console.error('Error fetching document to sign:', fetchError);
        return null;
      }

      // الحصول على بيانات المستخدم
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        console.error('User not authenticated');
        return null;
      }

      // إنشاء التوقيع
      const signature = createSignature(
        documentContent,
        documentTitle,
        user,
        signedDoc.recipient_id,
        signedDoc.recipient_unit_id,
        password
      );

      // تحديث المستند بالتوقيع
      const { data: updatedDoc, error: updateError } = await supabase
        .from('signed_documents')
        .update({
          status: 'signed',
          signature,
          signed_at: new Date().toISOString()
        })
        .eq('id', signedDocumentId)
        .select()
        .single();

      if (updateError) {
        console.error('Error signing document:', updateError);
        return null;
      }

      return updatedDoc;
    } catch (err) {
      console.error('Exception in signDocument:', err);
      return null;
    }
  }

  /**
   * إرسال مستند موقع إلى جهة محددة
   * @param signedDocumentId معرف المستند الموقع
   * @param recipientId معرف المستلم (اختياري)
   * @param recipientUnitId معرف وحدة المستلم (اختياري)
   */
  static async sendSignedDocument(
    signedDocumentId: string,
    recipientId?: string,
    recipientUnitId?: string
  ): Promise<SignedDocument | null> {
    try {
      // التحقق من وجود المستند وأنه موقع
      const { data: signedDoc, error: fetchError } = await supabase
        .from('signed_documents')
        .select('*')
        .eq('id', signedDocumentId)
        .eq('status', 'signed')
        .single();

      if (fetchError || !signedDoc) {
        console.error('Error fetching signed document:', fetchError);
        return null;
      }

      // تحديث المستند
      const updateData: any = {
        status: 'sent',
        // تحديث حالة المراجعة
        status: 'under_review'
      };

      // إضافة معلومات المستلم إذا تم تمريرها
      if (recipientId) {
        updateData.recipient_id = recipientId;
      }

      if (recipientUnitId) {
        updateData.recipient_unit_id = recipientUnitId;
      }

      const { data: updatedDoc, error: updateError } = await supabase
        .from('signed_documents')
        .update(updateData)
        .eq('id', signedDocumentId)
        .select()
        .single();

      if (updateError) {
        console.error('Error sending signed document:', updateError);
        return null;
      }

      return updatedDoc;
    } catch (err) {
      console.error('Exception in sendSignedDocument:', err);
      return null;
    }
  }

  /**
   * مراجعة مستند موقع (موافقة، رفض، إرجاع للتعديل)
   * @param signedDocumentId معرف المستند الموقع
   * @param reviewerId معرف المراجع
   * @param action الإجراء (approved, rejected, returned_for_edit)
   * @param notes ملاحظات المراجعة
   */
  static async reviewSignedDocument(
    signedDocumentId: string,
    reviewerId: string,
    action: 'approved' | 'rejected' | 'returned_for_edit',
    notes?: string
  ): Promise<SignedDocument | null> {
    try {
      // التحقق من وجود المستند وأنه قيد المراجعة
      const { data: signedDoc, error: fetchError } = await supabase
        .from('signed_documents')
        .select(`
          *,
          recipient_unit:recipient_unit_id(id, name)
        `)
        .eq('id', signedDocumentId)
        .eq('status', 'under_review')
        .single();

      if (fetchError || !signedDoc) {
        console.error('Error fetching document under review:', fetchError);
        return null;
      }

      // التحقق من أن المراجع هو المستلم أو ينتمي إلى وحدة المستلم
      const { data: profile } = await supabase
        .from('profiles')
        .select('unit_id, role_id')
        .eq('id', reviewerId)
        .single();

      if (!profile) {
        console.error('Reviewer profile not found');
        return null;
      }

      // التحقق من الصلاحية
      const isRecipient = signedDoc.recipient_id === reviewerId;
      const isInRecipientUnit = signedDoc.recipient_unit_id && profile.unit_id === signedDoc.recipient_unit_id;

      // التحقق من دور المستخدم
      let isAdmin = false;

      if (profile.role_id) {
        const { data: role } = await supabase
          .from('roles')
          .select('name')
          .eq('id', profile.role_id)
          .single();

        if (role) {
          isAdmin = role.name === 'admin' || role.name === 'مشرف';
        }
      }

      if (!isRecipient && !isInRecipientUnit && !isAdmin) {
        console.error('User not authorized to review this document');
        return null;
      }

      // تحديث المستند
      const { data: updatedDoc, error: updateError } = await supabase
        .from('signed_documents')
        .update({
          status: action,
          reviewer_id: reviewerId,
          reviewed_at: new Date().toISOString(),
          review_notes: notes || null
        })
        .eq('id', signedDocumentId)
        .select()
        .single();

      if (updateError) {
        console.error('Error reviewing signed document:', updateError);
        return null;
      }

      return updatedDoc;
    } catch (err) {
      console.error('Exception in reviewSignedDocument:', err);
      return null;
    }
  }

  /**
   * الحصول على مستند موقع بواسطة المعرف
   * @param signedDocumentId معرف المستند الموقع
   */
  static async getSignedDocumentById(signedDocumentId: string): Promise<SignedDocument | null> {
    try {
      const { data, error } = await supabase
        .from('signed_documents')
        .select(`
          *,
          document:document_id(*),
          creator:creator_id(id, full_name, email),
          signer:signer_id(id, full_name, email),
          recipient:recipient_id(id, full_name, email),
          recipient_unit:recipient_unit_id(id, name),
          reviewer:reviewer_id(id, full_name, email)
        `)
        .eq('id', signedDocumentId)
        .single();

      if (error) {
        console.error('Error fetching signed document:', error);
        return null;
      }

      return data;
    } catch (err) {
      console.error('Exception in getSignedDocumentById:', err);
      return null;
    }
  }

  /**
   * الحصول على تاريخ مستند موقع
   * @param signedDocumentId معرف المستند الموقع
   */
  static async getSignedDocumentHistory(signedDocumentId: string): Promise<SignedDocumentHistory[]> {
    try {
      const { data, error } = await supabase
        .from('signed_document_history')
        .select(`
          *,
          user:user_id(id, full_name, email)
        `)
        .eq('signed_document_id', signedDocumentId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching signed document history:', error);
        return [];
      }

      return data || [];
    } catch (err) {
      console.error('Exception in getSignedDocumentHistory:', err);
      return [];
    }
  }
}
