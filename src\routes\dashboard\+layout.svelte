<script lang="ts">
  import { goto } from '$app/navigation';
  import { supabase } from '$lib/supabase';
  import { onMount } from 'svelte';
  import ThemeToggle from '$lib/components/ThemeToggle.svelte';
  import { PermissionService } from '$lib/services/permissionService';
  import { Action, Resource, RoleType } from '$lib/types/permissions';

  let user: any = null;
  let loading = true;
  let isSidebarOpen = true;
  let activeRoute = '';
  let isAdmin = false;

  // تعريف عناصر القائمة الجانبية
  const menuItems = [
    {
      title: 'الرئيسية',
      href: '/dashboard',
      icon: '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/><polyline points="9 22 9 12 15 12 15 22"/></svg>'
    },
    {
      title: 'المستندات',
      href: '/dashboard/documents',
      icon: '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/><polyline points="14 2 14 8 20 8"/><path d="M16 13H8"/><path d="M16 17H8"/><path d="M10 9H8"/></svg>'
    },
    {
      title: 'المستندات الموقعة',
      href: '/dashboard/documents/signed',
      icon: '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M17.414 2.586a2 2 0 00-2.828 0L7 10.172V13h2.828l7.586-7.586a2 2 0 000-2.828z"/><path d="M2 6a2 2 0 012-2h4a1 1 0 010 2H4v10h10v-4a1 1 0 112 0v4a2 2 0 01-2 2H4a2 2 0 01-2-2V6z"/></svg>'
    },
    {
      title: 'اختبار قاعدة البيانات',
      href: '/dashboard/test-db',
      icon: '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 14.66V20a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h5.34"></path><polygon points="18 2 22 6 12 16 8 16 8 12 18 2"></polygon></svg>'
    },

    {
      title: 'المراسلات',
      href: '/dashboard/messages',
      icon: '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 5H3v14h18V5z"/><path d="m3 5 9 9 9-9"/></svg>'
    },
    {
      title: 'التعميمات',
      href: '/dashboard/broadcasts',
      icon: '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"/><path d="M19 10v2a7 7 0 0 1-14 0v-2"/><line x1="12" y1="19" x2="12" y2="23"/><line x1="8" y1="23" x2="16" y2="23"/></svg>'
    },
    {
      title: 'الدردشة',
      href: '/dashboard/chat',
      icon: '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/></svg>'
    },
    {
      title: 'الهيكل التنظيمي',
      href: '/dashboard/organization',
      icon: '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 3H3v7h18V3z"/><path d="M21 14h-5v7h5v-7z"/><path d="M8 14H3v7h5v-7z"/><path d="M14.5 14.5h-5v7h5v-7z"/></svg>'
    },
    {
      title: 'المستخدمين',
      href: '/dashboard/users',
      icon: '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/><circle cx="9" cy="7" r="4"/><path d="M22 21v-2a4 4 0 0 0-3-3.87"/><path d="M16 3.13a4 4 0 0 1 0 7.75"/></svg>'
    },
    {
      title: 'الأدوار والصلاحيات',
      href: '/dashboard/roles',
      icon: '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10"/><path d="m9 12 2 2 4-4"/></svg>'
    },
    {
      title: 'لوحة تحكم المدير',
      href: '/admin',
      icon: '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="18" height="18" x="3" y="3" rx="2" ry="2"></rect><line x1="3" x2="21" y1="9" y2="9"></line><line x1="9" x2="9" y1="21" y2="9"></line></svg>',
      adminOnly: true
    },
    {
      title: 'الإعدادات',
      href: '/dashboard/settings',
      icon: '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"/><circle cx="12" cy="12" r="3"/></svg>'
    }
  ];

  onMount(async () => {
    try {
      // تحديد المسار النشط
      activeRoute = window.location.pathname;

      // جلب بيانات المستخدم
      const { data } = await supabase.auth.getSession();

      if (data.session) {
        user = data.session.user;

        // التحقق من صلاحيات المستخدم
        isAdmin = await PermissionService.checkRole(user.id, RoleType.ADMIN);

        // التحقق من صلاحيات الوصول إلى الصفحات المحمية
        if (activeRoute.includes('/dashboard/roles') && !isAdmin) {
          // إذا كان المستخدم يحاول الوصول إلى صفحة الأدوار والصلاحيات وليس لديه صلاحية المدير
          alert('ليس لديك صلاحية للوصول إلى هذه الصفحة');
          goto('/dashboard');
        }

        if (activeRoute.includes('/dashboard/users')) {
          const hasUsersPermission = await PermissionService.checkPermission(
            user.id,
            Resource.USERS,
            Action.READ
          );

          if (!hasUsersPermission && !isAdmin) {
            alert('ليس لديك صلاحية للوصول إلى هذه الصفحة');
            goto('/dashboard');
          }
        }
      } else {
        goto('/login');
      }
    } catch (error) {
      console.error('Error checking permissions:', error);
    } finally {
      loading = false;
    }

    // إعداد مستمع لتغييرات المصادقة
    supabase.auth.onAuthStateChange((event, session) => {
      if (event === 'SIGNED_OUT') {
        goto('/login');
      } else if (session) {
        user = session.user;
      }
    });
  });

  async function handleSignOut() {
    await supabase.auth.signOut();
    goto('/login');
  }

  function toggleSidebar() {
    isSidebarOpen = !isSidebarOpen;
  }
</script>

{#if loading}
  <div class="flex items-center justify-center min-h-screen">
    <div class="text-center">
      <div class="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mb-4"></div>
      <p class="text-lg">جاري التحميل...</p>
    </div>
  </div>
{:else}
  <div class="min-h-screen flex flex-col bg-white dark:bg-gray-900 transition-colors duration-300">
    <!-- Header -->
    <header class="bg-card border-b border-gray-200/40 py-3 px-4 shadow-sm sticky top-0 z-10 dark:bg-gray-800 dark:border-gray-700/40">
      <div class="container mx-auto flex justify-between items-center">
        <div class="flex items-center">
          <button
            class="p-2 rounded-md hover:bg-muted mr-2 lg:hidden"
            on:click={toggleSidebar}
            aria-label="Toggle sidebar"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="4" x2="20" y1="12" y2="12"/><line x1="4" x2="20" y1="6" y2="6"/><line x1="4" x2="20" y1="18" y2="18"/></svg>
          </button>
          <a href="/dashboard" class="flex items-center">
            <div class="bg-primary/10 p-2 rounded-md mr-2 dark:bg-blue-900/30">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary"><path d="M16 6h3a1 1 0 0 1 1 1v11a2 2 0 0 1-2 2h-4a2 2 0 0 1-2-2V5a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v1Z"/><path d="M11 8H7a1 1 0 0 0-1 1v10a2 2 0 0 0 2 2h4a2 2 0 0 0 2-2V9a1 1 0 0 0-1-1Z"/></svg>
            </div>
            <h1 class="text-xl font-bold hidden md:block">نظام الأرشفة الإلكترونية</h1>
          </a>
        </div>

        <div class="flex items-center gap-4">
          <ThemeToggle />

          {#if user}
            <div class="flex items-center gap-2 bg-muted/50 px-3 py-1.5 rounded-full dark:bg-gray-800">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-muted-foreground dark:text-gray-400"><path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"/><circle cx="12" cy="7" r="4"/></svg>
              <span class="text-sm hidden sm:inline-block">{user.email}</span>
            </div>
            <button
              class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-white transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-gray-200 bg-white hover:bg-gray-100 hover:text-gray-900 h-9 rounded-md px-3 dark:border-gray-700 dark:bg-gray-800 dark:hover:bg-gray-700 dark:text-gray-200"
              on:click={handleSignOut}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-1"><path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"/><polyline points="16 17 21 12 16 7"/><line x1="21" x2="9" y1="12" y2="12"/></svg>
              <span class="hidden sm:inline-block">تسجيل الخروج</span>
            </button>
          {/if}
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <div class="flex flex-1 overflow-hidden">
      <!-- Sidebar -->
      <aside
        class="bg-card border-l border-gray-200/40 w-64 shrink-0 overflow-y-auto transition-all duration-300 ease-in-out lg:relative fixed inset-y-0 right-0 z-20 transform lg:translate-x-0 dark:bg-gray-800 dark:border-gray-700/40"
        class:translate-x-0={isSidebarOpen}
        class:translate-x-full={!isSidebarOpen}
      >
        <div class="p-4">
          <div class="mb-6 text-center">
            <div class="inline-block bg-primary/10 p-3 rounded-full mb-2 dark:bg-blue-900/30">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/><circle cx="9" cy="7" r="4"/><path d="M22 21v-2a4 4 0 0 0-3-3.87"/><path d="M16 3.13a4 4 0 0 1 0 7.75"/></svg>
            </div>
            <p class="text-sm font-medium">{user?.email}</p>
          </div>

          <nav>
            <ul class="space-y-1">
              {#each menuItems as item}
                {#if !item.adminOnly || isAdmin}
                  <li>
                    <a
                      href={item.href}
                      class={`flex items-center p-2 rounded-md transition-colors ${
                        activeRoute === item.href
                          ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400'
                          : 'hover:bg-gray-100 dark:hover:bg-gray-700'
                      }`}
                    >
                      <span class="ml-3">{@html item.icon}</span>
                      <span>{item.title}</span>
                    </a>
                  </li>
                {/if}
              {/each}
            </ul>
          </nav>
        </div>
      </aside>

      <!-- Overlay for mobile sidebar -->
      {#if isSidebarOpen}
        <button
          type="button"
          class="fixed inset-0 bg-black/50 z-10 lg:hidden"
          on:click={toggleSidebar}
          aria-label="Close sidebar"
        ></button>
      {/if}

      <!-- Content -->
      <main class="flex-1 overflow-y-auto p-6 bg-white dark:bg-gray-900 dark:text-gray-200">
        <slot />
      </main>
    </div>
  </div>
{/if}
