<script lang="ts">
  import { onMount } from 'svelte';
  import { supabase } from '$lib/supabase';
  import { goto } from '$app/navigation';

  // متغيرات لتخزين بيانات المستند
  let logoUrl: string = '';
  let secondLogoUrl: string = ''; // رابط الشعار الثاني
  let effectiveLogoUrl: string = '';
  let effectiveSecondLogoUrl: string = '';

  // متغيرات لتخزين معلومات الاتصال والنصوص
  let organizationAddress: string = '';
  let organizationPhone: string = '';
  let organizationEmail: string = '';
  let organizationWebsite: string = '';
  let documentTitle: string = 'عنوان المستند';
  let fontSize: number = 16;
  let lineHeight: number = 1.5;
  let fontFamily: string = 'Tajawal, Tahoma, Arial, sans-serif';
  let showPageNumbers: boolean = true;
  let documentDate: string = new Date().toISOString().split('T')[0];
  let documentRef: string = '';
  let documentAuthor: string = '';
  let documentContent: string = '';
  let pages: Array<{content: string}> = [{ content: '' }]; // مصفوفة لتخزين محتوى الصفحات
  let currentPage: number = 0;

  // متغيرات إضافية للمنظمة
  let organizationName: string = 'دولة ليبيا';
  let ministryName: string = '';

  // قائمة الخطوط العربية المتاحة
  const arabicFonts = [
    { name: 'Tajawal', value: 'Tajawal, sans-serif' },
    { name: 'Cairo', value: 'Cairo, sans-serif' },
    { name: 'Almarai', value: 'Almarai, sans-serif' },
    { name: 'Amiri', value: 'Amiri, serif' },
    { name: 'Scheherazade', value: 'Scheherazade New, serif' },
    { name: 'Lateef', value: 'Lateef, serif' },
    { name: 'Tahoma', value: 'Tahoma, sans-serif' },
    { name: 'Arial', value: 'Arial, sans-serif' }
  ];

  // قائمة خيارات تباعد الأسطر
  const lineHeightOptions = [
    { name: 'ضيق', value: 1.2 },
    { name: 'متوسط', value: 1.5 },
    { name: 'واسع', value: 2.0 }
  ];

  // متغيرات للمحتويات المتقدمة
  let showAdvancedTools: boolean = false; // للتحكم في إظهار/إخفاء المحتويات المتقدمة
  let showInstructions: boolean = false; // للتحكم في إظهار/إخفاء تعليمات الاستخدام
  let textColor: string = '#000000'; // لون النص
  let backgroundColor: string = 'transparent'; // لون خلفية النص
  let textAlign: 'right' | 'center' | 'left' | 'justify' = 'right'; // محاذاة النص
  let fontWeight: 'normal' | 'bold' = 'normal'; // وزن الخط
  let fontStyle: 'normal' | 'italic' = 'normal'; // نمط الخط
  let textDecoration: 'none' | 'underline' = 'none'; // تزيين النص
  let tableRows: number = 3; // عدد صفوف الجدول
  let tableCols: number = 3; // عدد أعمدة الجدول

  // متغيرات للتوقيع الإلكتروني
  let showSignatureModal: boolean = false; // للتحكم في إظهار/إخفاء نافذة التوقيع
  let signaturePassword: string = ''; // كلمة مرور التوقيع
  let passwordProtected: boolean = false; // هل التوقيع محمي بكلمة مرور
  let signersList: any[] = []; // قائمة الموقعين المتاحين
  let selectedSignerId: string = ''; // معرف الموقع المحدد
  let isSending: boolean = false; // هل جاري إرسال المستند للتوقيع
  let currentUser: any = null; // المستخدم الحالي
  let documentId: string = ''; // معرف المستند المحفوظ

  // تحويل رابط Google Drive إلى رابط مباشر للصورة
  function convertGoogleDriveUrl(url: string): string {
    if (!url) return '';

    // التحقق من أن الرابط هو رابط Google Drive
    if (url.includes('drive.google.com/file/d/')) {
      // استخراج معرف الملف
      const fileIdMatch = url.match(/\/d\/([^\/]+)/);
      if (fileIdMatch && fileIdMatch[1]) {
        const fileId = fileIdMatch[1];
        // إنشاء رابط مباشر للصورة
        return `https://drive.google.com/uc?export=view&id=${fileId}`;
      }
    }

    // إذا لم يكن رابط Google Drive، أعد الرابط كما هو
    return url;
  }

  // دالة للطباعة
  function printDocument(): void {
    window.print();
  }

  // دالة لتغيير حجم الخط
  function changeFontSize(size: number): void {
    fontSize = size;

    // التحقق من وجود نص محدد
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) {
      console.log("لا يوجد تحديد، سيتم تطبيق التنسيق على الفقرة الحالية");

      // إذا لم يكن هناك تحديد، استخدم دالة applyTextFormat لتطبيق التنسيق على الفقرة الحالية
      applyTextFormat('fontSize', `${size}pt`);
      return;
    }

    const range = selection.getRangeAt(0);

    // التحقق مما إذا كان هناك نص محدد
    const hasSelection = !range.collapsed;
    const selectedText = hasSelection ? range.toString() : '';

    if (hasSelection && selectedText) {
      console.log(`تطبيق حجم الخط على النص المحدد: ${selectedText}`);

      // استخدام دالة applyTextFormat لتطبيق التنسيق على النص المحدد
      applyTextFormat('fontSize', `${size}pt`);
    } else {
      // إذا لم يكن هناك تحديد، استخدم دالة applyTextFormat لتطبيق التنسيق على الفقرة الحالية
      applyTextFormat('fontSize', `${size}pt`);
    }
  }

  // دالة لتغيير نوع الخط
  function changeFontFamily(font: string): void {
    fontFamily = font;

    // التحقق من وجود نص محدد
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) {
      console.log("لا يوجد تحديد، سيتم تطبيق التنسيق على الفقرة الحالية");

      // إذا لم يكن هناك تحديد، استخدم دالة applyTextFormat لتطبيق التنسيق على الفقرة الحالية
      applyTextFormat('fontFamily', font);
      return;
    }

    const range = selection.getRangeAt(0);

    // التحقق مما إذا كان هناك نص محدد
    const hasSelection = !range.collapsed;
    const selectedText = hasSelection ? range.toString() : '';

    if (hasSelection && selectedText) {
      console.log(`تطبيق نوع الخط على النص المحدد: ${selectedText}`);

      // إنشاء عنصر span جديد بنوع الخط المطلوب
      const span = document.createElement('span');
      span.style.fontFamily = font;

      // استبدال النص المحدد بالعنصر الجديد
      range.deleteContents();
      range.insertNode(span);
      span.appendChild(document.createTextNode(selectedText));

      // تحديث محتوى الصفحة
      updatePageContent();
    } else {
      // إذا لم يكن هناك تحديد، استخدم دالة applyTextFormat لتطبيق التنسيق على الفقرة الحالية
      applyTextFormat('fontFamily', font);
    }
  }

  // دالة لتغيير تباعد الأسطر
  function changeLineHeight(height: number): void {
    lineHeight = height;

    // التحقق من وجود نص محدد
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) {
      console.log("لا يوجد تحديد، سيتم تطبيق التنسيق على الفقرة الحالية");

      // إذا لم يكن هناك تحديد، استخدم دالة applyTextFormat لتطبيق التنسيق على الفقرة الحالية
      applyTextFormat('lineHeight', height.toString());
      return;
    }

    const range = selection.getRangeAt(0);

    // التحقق مما إذا كان هناك نص محدد
    const hasSelection = !range.collapsed;
    const selectedText = hasSelection ? range.toString() : '';

    if (hasSelection && selectedText) {
      console.log(`تطبيق تباعد الأسطر على النص المحدد: ${selectedText}`);

      // إنشاء عنصر span جديد بتباعد الأسطر المطلوب
      const span = document.createElement('span');
      span.style.lineHeight = height.toString();

      // استبدال النص المحدد بالعنصر الجديد
      range.deleteContents();
      range.insertNode(span);
      span.appendChild(document.createTextNode(selectedText));

      // تحديث محتوى الصفحة
      updatePageContent();
    } else {
      // إذا لم يكن هناك تحديد، استخدم دالة applyTextFormat لتطبيق التنسيق على الفقرة الحالية
      applyTextFormat('lineHeight', height.toString());
    }
  }

  // دالة للتعامل مع تغيير محتوى الصفحة
  function handleContentChange(event: Event, pageIndex: number): void {
    const target = event.target as HTMLDivElement;

    // إضافة خصائص RTL للعنصر الرئيسي إذا لم تكن موجودة
    if (!target.hasAttribute('dir')) {
      target.setAttribute('dir', 'rtl');
    }

    // التأكد من أن كل عنصر في المحتوى يحتوي على خصائص RTL
    const elements = target.querySelectorAll('p, div, span, h1, h2, h3, h4, h5, h6');
    elements.forEach(element => {
      if (!element.hasAttribute('dir')) {
        element.setAttribute('dir', 'rtl');
      }

      // تحويل العنصر إلى HTMLElement لاستخدام خاصية style
      const htmlElement = element as HTMLElement;
      if (!htmlElement.style.textAlign) {
        htmlElement.style.textAlign = 'right';
      }
    });

    // إذا كان المحتوى فارغاً، أضف فقرة افتراضية بخصائص RTL
    if (target.innerHTML.trim() === '') {
      target.innerHTML = '<p dir="rtl" style="text-align: right;">ابدأ الكتابة هنا...</p>';
    }

    // إذا كان المحتوى يحتوي على نص بدون وسوم، قم بتغليفه في وسم p
    if (target.childNodes.length === 1 && target.childNodes[0].nodeType === Node.TEXT_NODE) {
      target.innerHTML = `<p dir="rtl" style="text-align: right;">${target.textContent}</p>`;
    }

    pages[pageIndex].content = target.innerHTML;
    documentContent = pages.map(page => page.content).join('\n');
  }

  // دالة لإضافة صفحة جديدة
  function addNewPage(): void {
    pages = [...pages, { content: '' }];
    currentPage = pages.length - 1;
  }

  // دالة للانتقال إلى الصفحة التالية
  function nextPage(): void {
    if (currentPage < pages.length - 1) {
      currentPage++;
    }
  }

  // دالة للانتقال إلى الصفحة السابقة
  function prevPage(): void {
    if (currentPage > 0) {
      currentPage--;
    }
  }

  // دالة لإصلاح اتجاه النص في جميع الصفحات
  function fixTextDirection(): void {
    // تحديث اتجاه النص في الصفحة الحالية
    setTimeout(() => {
      const contentElements = document.querySelectorAll('.page-content');
      contentElements.forEach(element => {
        const htmlElement = element as HTMLElement;
        htmlElement.setAttribute('dir', 'rtl');

        // إضافة خصائص RTL لجميع العناصر داخل المحتوى
        const childElements = htmlElement.querySelectorAll('p, div, span, h1, h2, h3, h4, h5, h6');
        childElements.forEach(child => {
          child.setAttribute('dir', 'rtl');
          (child as HTMLElement).style.textAlign = 'right';
        });

        // إذا كان هناك نص مباشر بدون وسوم، قم بتغليفه في وسم p
        const textNodes = Array.from(htmlElement.childNodes).filter(node =>
          node.nodeType === Node.TEXT_NODE && node.textContent && node.textContent.trim() !== ''
        );

        textNodes.forEach(textNode => {
          const p = document.createElement('p');
          p.setAttribute('dir', 'rtl');
          p.style.textAlign = 'right';
          p.textContent = textNode.textContent || '';
          htmlElement.replaceChild(p, textNode);
        });
      });

      // تحديث محتوى الصفحات
      const currentContentElement = document.querySelector(`.page-content`) as HTMLDivElement;
      if (currentContentElement) {
        pages[currentPage].content = currentContentElement.innerHTML;
        documentContent = pages.map(page => page.content).join('\n');
      }
    }, 0);
  }

  // دالة للتمرير إلى أعلى الصفحة
  function scrollToTop(): void {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }

  // دالة للتمرير إلى أسفل الصفحة
  function scrollToBottom(): void {
    window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' });
  }

  // دالة للتمرير إلى المستند
  function scrollToDocument(): void {
    const pageContainer = document.querySelector('.page-container');
    if (pageContainer) {
      pageContainer.scrollIntoView({ behavior: 'smooth' });
    }
  }

  // دالة لتبديل إظهار/إخفاء المحتويات المتقدمة
  function toggleAdvancedTools(): void {
    showAdvancedTools = !showAdvancedTools;
  }

  // دالة لتبديل إظهار/إخفاء تعليمات الاستخدام
  function toggleInstructions(): void {
    showInstructions = !showInstructions;
  }

  // دالة لتطبيق تنسيق النص
  function applyTextFormat(format: string, value: string): void {
    console.log(`تطبيق تنسيق ${format} بقيمة ${value}`);

    // تحديث المتغير العام لحجم الخط
    if (format === 'fontSize') {
      fontSize = parseInt(value.replace('pt', ''));
    }

    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) {
      console.log("لا يوجد تحديد");
      // لا نقوم بأي إجراء إذا لم يكن هناك تحديد
      return;
    }

    const range = selection.getRangeAt(0);

    // التحقق مما إذا كان هناك نص محدد
    const hasSelection = !range.collapsed;
    const selectedText = hasSelection ? range.toString() : '';

    console.log(`هل يوجد تحديد: ${hasSelection}, النص المحدد: ${selectedText}`);

    // إذا كان هناك نص محدد، طبق التنسيق على النص المحدد فقط
    if (hasSelection && selectedText) {
      console.log(`تطبيق التنسيق على النص المحدد: ${selectedText}`);

      // إنشاء عنصر span جديد بالتنسيق المطلوب
      const span = document.createElement('span');

      switch (format) {
        case 'color':
          span.style.color = value;
          textColor = value;
          break;
        case 'background':
          span.style.backgroundColor = value;
          backgroundColor = value;
          break;
        case 'bold':
          span.style.fontWeight = value;
          fontWeight = value as 'normal' | 'bold';
          break;
        case 'italic':
          span.style.fontStyle = value;
          fontStyle = value as 'normal' | 'italic';
          break;
        case 'underline':
          span.style.textDecoration = value;
          textDecoration = value as 'none' | 'underline';
          break;
        case 'fontSize':
          span.style.fontSize = value;
          break;
        case 'fontFamily':
          span.style.fontFamily = value;
          break;
        case 'lineHeight':
          span.style.lineHeight = value;
          break;
        case 'align':
          // محاذاة النص تطبق على الفقرة بأكملها
          const paragraph = findParentByTagName(range.commonAncestorContainer.parentElement, ['P', 'DIV', 'H1', 'H2', 'H3', 'H4', 'H5', 'H6']);
          if (paragraph) {
            paragraph.style.textAlign = value;
            textAlign = value as 'right' | 'center' | 'left' | 'justify';
            updatePageContent();
            return;
          }
          break;
      }

      // استبدال النص المحدد بالعنصر الجديد
      range.deleteContents();
      range.insertNode(span);
      span.appendChild(document.createTextNode(selectedText));

      // تحديث محتوى الصفحة
      updatePageContent();
      return;
    }

    // إذا لم يكن هناك نص محدد، تعامل مع الحالات الخاصة

    // محاولة العثور على العنصر الحالي
    let currentElement: HTMLElement | null = null;

    if (range.commonAncestorContainer.nodeType === Node.TEXT_NODE) {
      currentElement = range.commonAncestorContainer.parentElement;
    } else if (range.commonAncestorContainer.nodeType === Node.ELEMENT_NODE) {
      currentElement = range.commonAncestorContainer as HTMLElement;
    }

    // إذا كان المؤشر داخل خلية جدول
    const tableCell = findParentByTagName(currentElement, ['TD', 'TH']);
    if (tableCell) {
      console.log("تطبيق التنسيق على خلية الجدول");

      if (format === 'fontSize') {
        tableCell.style.fontSize = value;
      } else if (format === 'align') {
        tableCell.style.textAlign = value;
        textAlign = value as 'right' | 'center' | 'left' | 'justify';
      } else if (format === 'color') {
        tableCell.style.color = value;
      } else if (format === 'background') {
        tableCell.style.backgroundColor = value;
      } else if (format === 'fontFamily') {
        tableCell.style.fontFamily = value;
      } else if (format === 'lineHeight') {
        tableCell.style.lineHeight = value;
      }

      updatePageContent();
      return;
    }

    // إذا كان المؤشر داخل فقرة
    const paragraph = findParentByTagName(currentElement, ['P', 'DIV', 'H1', 'H2', 'H3', 'H4', 'H5', 'H6']);
    if (paragraph) {
      console.log("تطبيق التنسيق على الفقرة الحالية");

      if (format === 'fontSize') {
        paragraph.style.fontSize = value;
      } else if (format === 'align') {
        paragraph.style.textAlign = value;
        textAlign = value as 'right' | 'center' | 'left' | 'justify';
      } else if (format === 'color') {
        paragraph.style.color = value;
      } else if (format === 'background') {
        paragraph.style.backgroundColor = value;
      } else if (format === 'fontFamily') {
        paragraph.style.fontFamily = value;
      } else if (format === 'lineHeight') {
        paragraph.style.lineHeight = value;
      }

      updatePageContent();
      return;
    }

    // إذا لم يتم العثور على عنصر محدد، لا تفعل شيئاً
    console.log("لم يتم العثور على عنصر لتطبيق التنسيق عليه");
  }

  // دالة مساعدة للعثور على العنصر الأب بنوع معين
  function findParentByTagName(element: HTMLElement | null, tagNames: string[]): HTMLElement | null {
    if (!element) return null;

    let current: HTMLElement | null = element;
    while (current) {
      if (tagNames.includes(current.tagName)) {
        return current;
      }
      current = current.parentElement;
    }

    return null;
  }



  // دالة لإنشاء جدول
  function insertTable(): void {
    console.log("بدء إدراج الجدول...");

    // الحصول على العنصر النشط (حيث يوجد المؤشر)
    const contentElement = document.querySelector(`.page-content[contenteditable="true"]`) as HTMLDivElement;
    if (!contentElement) {
      console.error("لم يتم العثور على عنصر المحتوى");
      // محاولة أخرى للعثور على عنصر المحتوى
      const allContentElements = document.querySelectorAll('.page-content');
      if (allContentElements.length > 0) {
        console.log(`تم العثور على ${allContentElements.length} عنصر محتوى`);
        // استخدام عنصر المحتوى للصفحة الحالية
        const currentContentElement = document.querySelector(`.page[data-page="${currentPage}"] .page-content`) as HTMLDivElement;
        if (currentContentElement) {
          console.log("تم العثور على عنصر المحتوى للصفحة الحالية");
          insertTableIntoElement(currentContentElement);
        } else {
          console.error("لم يتم العثور على عنصر المحتوى للصفحة الحالية");
          // استخدام أول عنصر محتوى
          insertTableIntoElement(allContentElements[0] as HTMLDivElement);
        }
      } else {
        console.error("لم يتم العثور على أي عنصر محتوى");
        return;
      }
    } else {
      console.log("تم العثور على عنصر المحتوى");
      insertTableIntoElement(contentElement);
    }
  }

  // دالة مساعدة لإدراج الجدول في عنصر محدد
  function insertTableIntoElement(contentElement: HTMLDivElement): void {
    console.log("إدراج الجدول في العنصر...");

    // إنشاء جدول
    const table = document.createElement('table');
    table.setAttribute('dir', 'rtl');
    table.style.width = '100%';
    table.style.borderCollapse = 'collapse';
    table.style.marginBottom = '10mm';
    table.style.fontSize = `${fontSize}pt`; // استخدام حجم الخط الحالي
    table.className = 'editable-table'; // إضافة فئة للتعرف على الجدول
    table.setAttribute('data-table-id', `table-${Date.now()}`); // إضافة معرف فريد للجدول

    // إنشاء صفوف وأعمدة الجدول
    for (let i = 0; i < tableRows; i++) {
      const row = document.createElement('tr');

      for (let j = 0; j < tableCols; j++) {
        const cell = document.createElement(i === 0 ? 'th' : 'td');
        cell.style.border = '1px solid #000';
        cell.style.padding = '8px';
        cell.style.textAlign = 'right';
        cell.style.minWidth = '50px'; // حد أدنى للعرض
        cell.setAttribute('contenteditable', 'true');
        cell.textContent = i === 0 ? `عنوان ${j + 1}` : `خلية ${i},${j}`;
        row.appendChild(cell);
      }

      table.appendChild(row);
    }

    // إضافة أدوات التحكم في الجدول
    const tableControls = document.createElement('div');
    tableControls.className = 'table-controls';
    tableControls.style.marginBottom = '5mm';
    tableControls.style.display = 'flex';
    tableControls.style.gap = '5px';
    tableControls.style.justifyContent = 'center';

    // زر لإضافة صف
    const addRowBtn = document.createElement('button');
    addRowBtn.textContent = 'إضافة صف';
    addRowBtn.className = 'table-control-btn';
    addRowBtn.type = 'button'; // تحديد النوع لمنع إرسال النموذج
    addRowBtn.onclick = (e) => {
      e.preventDefault();
      const tableId = table.getAttribute('data-table-id') || '';
      if (tableId) addTableRow(tableId);
    };
    tableControls.appendChild(addRowBtn);

    // زر لإضافة عمود
    const addColBtn = document.createElement('button');
    addColBtn.textContent = 'إضافة عمود';
    addColBtn.className = 'table-control-btn';
    addColBtn.type = 'button'; // تحديد النوع لمنع إرسال النموذج
    addColBtn.onclick = (e) => {
      e.preventDefault();
      const tableId = table.getAttribute('data-table-id') || '';
      if (tableId) addTableColumn(tableId);
    };
    tableControls.appendChild(addColBtn);

    // زر لحذف صف
    const deleteRowBtn = document.createElement('button');
    deleteRowBtn.textContent = 'حذف صف';
    deleteRowBtn.className = 'table-control-btn';
    deleteRowBtn.type = 'button'; // تحديد النوع لمنع إرسال النموذج
    deleteRowBtn.onclick = (e) => {
      e.preventDefault();
      const tableId = table.getAttribute('data-table-id') || '';
      if (tableId) deleteTableRow(tableId);
    };
    tableControls.appendChild(deleteRowBtn);

    // زر لحذف عمود
    const deleteColBtn = document.createElement('button');
    deleteColBtn.textContent = 'حذف عمود';
    deleteColBtn.className = 'table-control-btn';
    deleteColBtn.type = 'button'; // تحديد النوع لمنع إرسال النموذج
    deleteColBtn.onclick = (e) => {
      e.preventDefault();
      const tableId = table.getAttribute('data-table-id') || '';
      if (tableId) deleteTableColumn(tableId);
    };
    tableControls.appendChild(deleteColBtn);

    // زر لحذف الجدول
    const deleteTableBtn = document.createElement('button');
    deleteTableBtn.textContent = 'حذف الجدول';
    deleteTableBtn.className = 'table-control-btn delete-btn';
    deleteTableBtn.type = 'button'; // تحديد النوع لمنع إرسال النموذج
    deleteTableBtn.onclick = (e) => {
      e.preventDefault();
      const tableId = table.getAttribute('data-table-id') || '';
      if (tableId) deleteTable(tableId);
    };
    tableControls.appendChild(deleteTableBtn);

    // إنشاء حاوية للجدول وأدوات التحكم
    const tableContainer = document.createElement('div');
    tableContainer.className = 'table-container';
    tableContainer.appendChild(table);
    tableContainer.appendChild(tableControls);

    // إدراج الجدول في المحتوى عند موضع المؤشر
    const selection = window.getSelection();
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);

      // التأكد من أن النطاق داخل محتوى الصفحة
      if (contentElement.contains(range.commonAncestorContainer)) {
        console.log("إدراج الجدول في موضع المؤشر");
        range.deleteContents();
        range.insertNode(tableContainer);
      } else {
        // إذا لم يكن المؤشر داخل المحتوى، أضف الجدول في نهاية المحتوى
        console.log("إدراج الجدول في نهاية المحتوى (المؤشر خارج المحتوى)");
        contentElement.appendChild(tableContainer);
      }
    } else {
      // إذا لم يكن هناك تحديد، أضف الجدول في نهاية المحتوى
      console.log("إدراج الجدول في نهاية المحتوى (لا يوجد تحديد)");
      contentElement.appendChild(tableContainer);
    }

    // تحديث محتوى الصفحة
    console.log("تحديث محتوى الصفحة");
    pages[currentPage].content = contentElement.innerHTML;
    documentContent = pages.map(page => page.content).join('\n');
  }

  // دالة لإضافة صف إلى الجدول
  function addTableRow(tableId: string): void {
    const table = document.querySelector(`table[data-table-id="${tableId}"]`) as HTMLTableElement;
    if (!table) return;

    const rowCount = table.rows.length;
    const colCount = table.rows[0].cells.length;

    const newRow = table.insertRow(-1); // إضافة صف في النهاية

    // إضافة خلايا للصف الجديد
    for (let i = 0; i < colCount; i++) {
      const cell = newRow.insertCell(i);
      cell.style.border = '1px solid #000';
      cell.style.padding = '8px';
      cell.style.textAlign = 'right';
      cell.setAttribute('contenteditable', 'true');
      cell.textContent = `خلية ${rowCount},${i}`;
    }

    // تحديث محتوى الصفحة
    updatePageContent();
  }

  // دالة لإضافة عمود إلى الجدول
  function addTableColumn(tableId: string): void {
    const table = document.querySelector(`table[data-table-id="${tableId}"]`) as HTMLTableElement;
    if (!table) return;

    const rows = table.rows;
    const colIndex = rows[0].cells.length;

    // إضافة خلية جديدة لكل صف
    for (let i = 0; i < rows.length; i++) {
      const cell = rows[i].insertCell(-1); // إضافة خلية في نهاية الصف
      cell.style.border = '1px solid #000';
      cell.style.padding = '8px';
      cell.style.textAlign = 'right';
      cell.setAttribute('contenteditable', 'true');
      cell.textContent = i === 0 ? `عنوان ${colIndex + 1}` : `خلية ${i},${colIndex}`;
    }

    // تحديث محتوى الصفحة
    updatePageContent();
  }

  // دالة لحذف صف من الجدول
  function deleteTableRow(tableId: string): void {
    const table = document.querySelector(`table[data-table-id="${tableId}"]`) as HTMLTableElement;
    if (!table || table.rows.length <= 1) return; // لا تحذف الصف الأخير

    table.deleteRow(-1); // حذف آخر صف

    // تحديث محتوى الصفحة
    updatePageContent();
  }

  // دالة لحذف عمود من الجدول
  function deleteTableColumn(tableId: string): void {
    const table = document.querySelector(`table[data-table-id="${tableId}"]`) as HTMLTableElement;
    if (!table || table.rows[0].cells.length <= 1) return; // لا تحذف العمود الأخير

    const colIndex = table.rows[0].cells.length - 1;

    // حذف آخر خلية من كل صف
    for (let i = 0; i < table.rows.length; i++) {
      table.rows[i].deleteCell(colIndex);
    }

    // تحديث محتوى الصفحة
    updatePageContent();
  }

  // دالة لحذف الجدول
  function deleteTable(tableId: string): void {
    const table = document.querySelector(`table[data-table-id="${tableId}"]`);
    if (!table) return;

    const tableContainer = table.closest('.table-container');
    if (!tableContainer) return;

    tableContainer.remove();

    // تحديث محتوى الصفحة
    updatePageContent();
  }

  // دالة لتحديث محتوى الصفحة
  function updatePageContent(): void {
    console.log("تحديث محتوى الصفحة...");

    // محاولة العثور على عنصر المحتوى للصفحة الحالية
    let contentElement = document.querySelector(`.page[data-page="${currentPage}"] .page-content`) as HTMLDivElement;

    // إذا لم يتم العثور على العنصر، حاول العثور على أي عنصر محتوى
    if (!contentElement) {
      console.log("لم يتم العثور على عنصر المحتوى للصفحة الحالية، البحث عن بدائل...");

      // البحث عن عنصر المحتوى القابل للتحرير
      contentElement = document.querySelector('.page-content[contenteditable="true"]') as HTMLDivElement;

      // إذا لم يتم العثور على عنصر قابل للتحرير، استخدم أي عنصر محتوى
      if (!contentElement) {
        console.log("لم يتم العثور على عنصر محتوى قابل للتحرير، البحث عن أي عنصر محتوى...");
        const allContentElements = document.querySelectorAll('.page-content');
        if (allContentElements.length > 0) {
          contentElement = allContentElements[0] as HTMLDivElement;
        }
      }
    }

    // إذا لم يتم العثور على أي عنصر محتوى، لا يمكن تحديث المحتوى
    if (!contentElement) {
      console.error("لم يتم العثور على أي عنصر محتوى، لا يمكن تحديث المحتوى");
      return;
    }

    console.log("تم العثور على عنصر المحتوى، تحديث المحتوى...");

    // تحديث محتوى الصفحة
    pages[currentPage].content = contentElement.innerHTML;
    documentContent = pages.map(page => page.content).join('\n');

    console.log("تم تحديث محتوى الصفحة بنجاح");
  }

  // دالة لإدراج قائمة
  function insertList(type: 'ul' | 'ol'): void {
    console.log(`بدء إدراج قائمة من نوع ${type}...`);

    // الحصول على العنصر النشط (حيث يوجد المؤشر)
    const contentElement = document.querySelector(`.page-content[contenteditable="true"]`) as HTMLDivElement;
    if (!contentElement) {
      console.error("لم يتم العثور على عنصر المحتوى");
      // محاولة أخرى للعثور على عنصر المحتوى
      const allContentElements = document.querySelectorAll('.page-content');
      if (allContentElements.length > 0) {
        console.log(`تم العثور على ${allContentElements.length} عنصر محتوى`);
        // استخدام عنصر المحتوى للصفحة الحالية
        const currentContentElement = document.querySelector(`.page[data-page="${currentPage}"] .page-content`) as HTMLDivElement;
        if (currentContentElement) {
          console.log("تم العثور على عنصر المحتوى للصفحة الحالية");
          insertListIntoElement(currentContentElement, type);
        } else {
          console.error("لم يتم العثور على عنصر المحتوى للصفحة الحالية");
          // استخدام أول عنصر محتوى
          insertListIntoElement(allContentElements[0] as HTMLDivElement, type);
        }
      } else {
        console.error("لم يتم العثور على أي عنصر محتوى");
        return;
      }
    } else {
      console.log("تم العثور على عنصر المحتوى");
      insertListIntoElement(contentElement, type);
    }
  }

  // دالة مساعدة لإدراج القائمة في عنصر محدد
  function insertListIntoElement(contentElement: HTMLDivElement, type: 'ul' | 'ol'): void {
    console.log(`إدراج قائمة من نوع ${type} في العنصر...`);

    // إنشاء قائمة
    const list = document.createElement(type);
    list.setAttribute('dir', 'rtl');
    list.style.marginBottom = '10mm';
    list.style.fontSize = `${fontSize}pt`; // استخدام حجم الخط الحالي

    // إضافة عناصر القائمة
    for (let i = 0; i < 3; i++) {
      const item = document.createElement('li');
      item.textContent = `عنصر القائمة ${i + 1}`;
      item.style.marginBottom = '5px';
      item.setAttribute('contenteditable', 'true');
      list.appendChild(item);
    }

    // إدراج القائمة في المحتوى عند موضع المؤشر
    const selection = window.getSelection();
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);

      // التأكد من أن النطاق داخل محتوى الصفحة
      if (contentElement.contains(range.commonAncestorContainer)) {
        console.log("إدراج القائمة في موضع المؤشر");
        range.deleteContents();
        range.insertNode(list);
      } else {
        // إذا لم يكن المؤشر داخل المحتوى، أضف القائمة في نهاية المحتوى
        console.log("إدراج القائمة في نهاية المحتوى (المؤشر خارج المحتوى)");
        contentElement.appendChild(list);
      }
    } else {
      // إذا لم يكن هناك تحديد، أضف القائمة في نهاية المحتوى
      console.log("إدراج القائمة في نهاية المحتوى (لا يوجد تحديد)");
      contentElement.appendChild(list);
    }

    // تحديث محتوى الصفحة
    console.log("تحديث محتوى الصفحة");
    updatePageContent();
  }

  // تهيئة الصفحات عند تحميل المكون
  onMount(async () => {
    console.log("تهيئة الصفحات...");

    // جلب إعدادات المنظمة
    await loadOrganizationSettings();

    // إضافة سمة data-page لكل صفحة
    const pageElements = document.querySelectorAll('.page');
    pageElements.forEach((page, index) => {
      page.setAttribute('data-page', index.toString());

      // جعل محتوى الصفحة قابل للتحرير
      const contentElement = page.querySelector('.page-content');
      if (contentElement) {
        contentElement.setAttribute('contenteditable', 'true');
      }
    });

    // تعيين حجم الخط الافتراضي للمحتوى
    const contentElements = document.querySelectorAll('.page-content');
    contentElements.forEach(element => {
      const htmlElement = element as HTMLElement;
      htmlElement.style.fontSize = `${fontSize}pt`;
    });

    // إضافة مستمع للنقر على المحتوى
    document.addEventListener('click', (e) => {
      const target = e.target as HTMLElement;

      // إذا كان النقر على زر في أدوات التحكم في الجدول، منع الحدث الافتراضي
      if (target.classList.contains('table-control-btn')) {
        e.preventDefault();
        return;
      }
    });

    console.log("تم تهيئة الصفحات بنجاح");
  });

  // جلب إعدادات المنظمة من قاعدة البيانات
  async function loadOrganizationSettings() {
    try {
      console.log("جلب إعدادات المنظمة...");

      // جلب إعدادات المنظمة
      const { data: settings, error: settingsError } = await supabase
        .from('organization_settings')
        .select('*')
        .single();

      if (settingsError) {
        console.error('Error fetching organization settings:', settingsError);
        return;
      }

      if (settings) {
        // تعيين اسم المنظمة واسم الوزارة
        if (settings.name) {
          organizationName = settings.name;
        }

        // تعيين نص الشعار
        if (settings.logo) {
          ministryName = settings.logo;
          console.log('تم تعيين نص الشعار من إعدادات المنظمة:', settings.logo);
        }

        // تعيين الشعارات
        if (settings.left_logo && settings.left_logo.startsWith('http')) {
          logoUrl = settings.left_logo;
          effectiveLogoUrl = settings.left_logo;
        }

        if (settings.right_logo && settings.right_logo.startsWith('http')) {
          secondLogoUrl = settings.right_logo;
          effectiveSecondLogoUrl = settings.right_logo;
        }

        // تعيين معلومات الاتصال والنصوص
        organizationAddress = settings.address || '';
        organizationPhone = settings.phone || '';
        organizationEmail = settings.email || '';
        organizationWebsite = settings.website || '';

        console.log("تم جلب إعدادات المنظمة بنجاح");
      }
    } catch (err) {
      console.error('Error loading organization settings:', err);
    }
  }

  // دالة لحفظ المستند محلياً
  function saveDocument(): void {
    console.log("حفظ المستند محلياً...");

    try {
      // إنشاء كائن يحتوي على بيانات المستند
      const documentData = {
        title: documentTitle,
        logoUrl: logoUrl,
        secondLogoUrl: secondLogoUrl,
        date: documentDate,
        ref: documentRef,
        author: documentAuthor,
        fontSize: fontSize,
        lineHeight: lineHeight,
        fontFamily: fontFamily,
        showPageNumbers: showPageNumbers,
        pages: pages
      };

      // تحويل البيانات إلى سلسلة JSON
      const jsonData = JSON.stringify(documentData, null, 2);

      // إنشاء Blob من البيانات
      const blob = new Blob([jsonData], { type: 'application/json' });

      // إنشاء رابط للتنزيل
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${documentTitle || 'مستند'}.json`;

      // إضافة الرابط إلى المستند والنقر عليه
      document.body.appendChild(a);
      a.click();

      // إزالة الرابط من المستند
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      console.log("تم حفظ المستند بنجاح");

      // عرض رسالة نجاح
      alert("تم حفظ المستند بنجاح");
    } catch (error) {
      console.error("حدث خطأ أثناء حفظ المستند:", error);
      alert("حدث خطأ أثناء حفظ المستند");
    }
  }

  // دالة لفتح المستند
  function openDocument(): void {
    console.log("فتح المستند...");

    try {
      // إنشاء عنصر input لاختيار الملف
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = '.json';

      // إضافة مستمع لحدث تغيير الملف
      input.onchange = (e) => {
        const target = e.target as HTMLInputElement;
        if (!target.files || target.files.length === 0) return;

        const file = target.files[0];
        const reader = new FileReader();

        reader.onload = (event) => {
          try {
            const result = event.target?.result as string;
            const documentData = JSON.parse(result);

            // التحقق من صحة البيانات
            if (!documentData || !documentData.pages) {
              throw new Error("ملف المستند غير صالح");
            }

            // تحديث بيانات المستند
            documentTitle = documentData.title || 'عنوان المستند';
            logoUrl = documentData.logoUrl || '';
            secondLogoUrl = documentData.secondLogoUrl || '';
            documentDate = documentData.date || new Date().toISOString().split('T')[0];
            documentRef = documentData.ref || '';
            documentAuthor = documentData.author || '';
            fontSize = documentData.fontSize || 16;
            lineHeight = documentData.lineHeight || 1.5;
            fontFamily = documentData.fontFamily || 'Tahoma, Arial, sans-serif';
            showPageNumbers = documentData.showPageNumbers !== undefined ? documentData.showPageNumbers : true;
            pages = documentData.pages || [{ content: '' }];
            currentPage = 0;

            console.log("تم فتح المستند بنجاح");

            // عرض رسالة نجاح
            alert("تم فتح المستند بنجاح");
          } catch (error) {
            console.error("حدث خطأ أثناء قراءة المستند:", error);
            alert("حدث خطأ أثناء قراءة المستند");
          }
        };

        reader.readAsText(file);
      };

      // النقر على عنصر input
      input.click();
    } catch (error) {
      console.error("حدث خطأ أثناء فتح المستند:", error);
      alert("حدث خطأ أثناء فتح المستند");
    }
  }

  // تحديث الشعارات عند تغيير الروابط
  $: effectiveLogoUrl = convertGoogleDriveUrl(logoUrl);
  $: effectiveSecondLogoUrl = convertGoogleDriveUrl(secondLogoUrl);

  // دالة لجلب المستخدمين الذين يمكنهم التوقيع
  async function fetchSigners() {
    try {
      // جلب المستخدمين الذين لديهم صلاحية التوقيع من لوحة تحكم المدير
      const { data: users, error } = await supabase.rpc('get_users_with_signature_permission');

      if (error) {
        console.error('Error fetching signers:', error);

        // في حالة حدوث خطأ، نحاول جلب المستخدمين بطريقة بديلة
        try {
          // جلب المستخدمين الذين لديهم صلاحية التوقيع من جدول signature_permissions
          const { data: permissionsData, error: permissionsError } = await supabase
            .from('signature_permissions')
            .select('user_id, can_sign')
            .eq('can_sign', true);

          if (permissionsError) {
            throw permissionsError;
          }

          // جلب بيانات المستخدمين
          if (permissionsData && permissionsData.length > 0) {
            const userIds = permissionsData.map(p => p.user_id);

            const { data: usersData, error: usersError } = await supabase
              .from('profiles')
              .select('id, full_name, email, role, unit_id')
              .in('id', userIds);

            if (usersError) {
              throw usersError;
            }

            signersList = usersData || [];
          } else {
            // إذا لم يكن هناك مستخدمين في جدول signature_permissions، نجلب المشرفين والمدراء
            const { data: adminsData, error: adminsError } = await supabase
              .from('profiles')
              .select('id, full_name, email, role, unit_id')
              .or('role.eq.admin,role.eq.مشرف,role.eq.manager,role.eq.مدير');

            if (adminsError) {
              throw adminsError;
            }

            signersList = adminsData || [];
          }
        } catch (fallbackErr) {
          console.error('Error in fallback signers fetch:', fallbackErr);
          signersList = [];
        }

        return;
      }

      // تصفية المستخدمين الذين لديهم صلاحية التوقيع فقط
      signersList = (users || []).filter((user: any) => user.can_sign === true);
    } catch (err) {
      console.error('Exception in fetchSigners:', err);
      signersList = [];
    }
  }

  // دالة لحفظ المستند في قاعدة البيانات
  async function saveDocumentToDatabase(): Promise<string | null> {
    try {
      // التحقق من وجود المستخدم
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        alert('يجب تسجيل الدخول لحفظ المستند');
        return null;
      }

      currentUser = user;

      // إنشاء كائن المستند
      const documentData = {
        title: documentTitle,
        content: documentContent,
        status: 'draft',
        created_by: user.id
      };

      // حفظ المستند في قاعدة البيانات
      const { data, error } = await supabase
        .from('documents')
        .insert(documentData)
        .select()
        .single();

      if (error) {
        console.error('Error saving document:', error);
        alert('حدث خطأ أثناء حفظ المستند');
        return null;
      }

      console.log('Document saved successfully:', data);
      documentId = data.id;
      return data.id;
    } catch (err) {
      console.error('Exception in saveDocumentToDatabase:', err);
      alert('حدث خطأ أثناء حفظ المستند');
      return null;
    }
  }

  // دالة لإرسال المستند للتوقيع
  async function sendDocumentForSignature() {
    try {
      isSending = true;

      // التحقق من اختيار موقع
      if (!selectedSignerId) {
        alert('يرجى اختيار موقع للمستند');
        isSending = false;
        return;
      }

      // التحقق من وجود المستخدم
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        alert('يجب تسجيل الدخول لإرسال المستند للتوقيع');
        isSending = false;
        return;
      }

      // جلب بيانات المستخدم المرسل
      const { data: senderProfile } = await supabase
        .from('profiles')
        .select('full_name, unit_id')
        .eq('id', user.id)
        .single();

      // جلب بيانات المستخدم المستقبل (الموقع) - للاستخدام المستقبلي إذا لزم الأمر

      // جلب إعدادات المنظمة للحصول على الشعارات ونص الشعار
      const { data: orgSettings } = await supabase
        .from('organization_settings')
        .select('*')
        .single();

      // إنشاء محتوى المستند بتنسيق HTML
      let htmlContent = '';
      pages.forEach((page, index) => {
        if (index > 0) {
          htmlContent += '<div style="page-break-before: always;"></div>';
        }
        htmlContent += page.content;
      });

      // إنشاء رسالة رسمية موقعة
      const messageData = {
        subject: documentTitle || 'مستند رسمي',
        content: htmlContent,
        sender_id: user.id,
        recipient_id: selectedSignerId,
        receiver_id: selectedSignerId,
        is_official: true,
        is_signed: true,
        unit_name: senderProfile?.unit_id ?
          (await supabase.from('units').select('name').eq('id', senderProfile.unit_id).single()).data?.name || '' : '',
        unit_logo: orgSettings?.left_logo || null,
        organization_logo: orgSettings?.right_logo || null,
        stamp: orgSettings?.default_stamp || null,
        signature: {
          signatureId: crypto.randomUUID(),
          timestamp: new Date().toISOString(),
          signer: user.id,
          passwordProtected: passwordProtected,
          documentHash: await generateDocumentHash(htmlContent + documentTitle),
          status: 'pending_signature'
        }
      };

      console.log('إنشاء رسالة رسمية موقعة:', messageData);

      // حفظ الرسالة في قاعدة البيانات
      const { data: savedMessage, error: messageError } = await supabase
        .from('messages')
        .insert(messageData)
        .select()
        .single();

      if (messageError) {
        console.error('خطأ في حفظ الرسالة:', messageError);
        alert('حدث خطأ أثناء إرسال المستند للتوقيع');
        isSending = false;
        return;
      }

      console.log('تم حفظ الرسالة بنجاح:', savedMessage);

      // إغلاق نافذة التوقيع وإظهار رسالة نجاح
      showSignatureModal = false;
      alert('تم إرسال المستند للتوقيع بنجاح كرسالة رسمية');

      // الانتقال إلى صفحة الرسائل
      goto('/dashboard/messages');
    } catch (err) {
      console.error('Exception in sendDocumentForSignature:', err);
      alert('حدث خطأ أثناء إرسال المستند للتوقيع');
    } finally {
      isSending = false;
    }
  }

  // دالة لإنشاء hash للمستند
  async function generateDocumentHash(content: string): Promise<string> {
    try {
      const encoder = new TextEncoder();
      const data = encoder.encode(content);
      const hashBuffer = await crypto.subtle.digest('SHA-256', data);
      const hashArray = Array.from(new Uint8Array(hashBuffer));
      const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
      return hashHex;
    } catch (error) {
      console.error('خطأ في إنشاء hash:', error);
      return crypto.randomUUID(); // fallback
    }
  }

  // دالة لفتح نافذة إرسال المستند للتوقيع
  function openSignatureModal() {
    // جلب المستخدمين الذين يمكنهم التوقيع
    fetchSigners();

    // فتح النافذة
    showSignatureModal = true;
  }

  // دالة للتحقق من صلاحيات المستخدم
  async function checkUserPermissions() {
    try {
      // جلب بيانات المستخدم الحالي
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return false;

      currentUser = user;

      // التحقق من دور المستخدم
      const { data: profile } = await supabase
        .from('profiles')
        .select('role, role_id')
        .eq('id', user.id)
        .single();

      if (!profile) return false;

      // التحقق من دور المستخدم
      if (profile.role === 'admin' || profile.role === 'مشرف' || profile.role === 'manager' || profile.role === 'مدير') {
        return true;
      }

      // التحقق من وجود دور
      if (profile.role_id) {
        const { data: role } = await supabase
          .from('roles')
          .select('name')
          .eq('id', profile.role_id)
          .single();

        if (role && (role.name === 'admin' || role.name === 'مشرف' || role.name === 'manager' || role.name === 'مدير')) {
          return true;
        }
      }

      return false;
    } catch (err) {
      console.error('Error checking user permissions:', err);
      return false;
    }
  }

  // متغير لتخزين ما إذا كان المستخدم مشرفاً
  let isAdmin = false;

  // تحميل البيانات عند تحميل الصفحة
  onMount(async () => {
    // جلب بيانات المستخدم الحالي
    const { data: { user } } = await supabase.auth.getUser();
    if (user) {
      currentUser = user;
      isAdmin = await checkUserPermissions();
    }

    // جلب بيانات المنظمة
    const { data: orgSettings } = await supabase
      .from('organization_settings')
      .select('*')
      .single();

    if (orgSettings) {
      // تحديث بيانات المنظمة
      organizationName = orgSettings.name || 'دولة ليبيا';
      ministryName = orgSettings.logo || ''; // استخدام حقل logo بدلاً من header_text
      logoUrl = orgSettings.left_logo || '';
      secondLogoUrl = orgSettings.right_logo || '';
      organizationAddress = orgSettings.address || '';
      organizationPhone = orgSettings.phone || '';
      organizationEmail = orgSettings.email || '';
      organizationWebsite = orgSettings.website || '';

      console.log('تم تحميل نص الشعار من إعدادات المنظمة:', ministryName);
    }
  });
</script>

<svelte:head>
  <title>محرر مستندات Word</title>
  <!-- استيراد الخطوط العربية من Google Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin="anonymous">
  <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&family=Cairo:wght@400;600;700&family=Almarai:wght@400;700&family=Amiri:wght@400;700&family=Lateef&family=Scheherazade+New&display=swap" rel="stylesheet">
  <style>

    /* تنسيقات عامة */
    body {
      margin: 0;
      padding: 0;
      font-family: 'Tajawal', Tahoma, Arial, sans-serif;
      direction: rtl;
      background-color: #f0f0f0;
    }

    /* تنسيقات الطباعة */
    @media print {
      body * {
        visibility: hidden;
      }

      .page-container, .page-container * {
        visibility: visible;
      }

      .page-container {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
      }

      .controls, .page-navigation, .no-print {
        display: none !important;
      }

      .page {
        margin: 0;
        box-shadow: none;
        page-break-after: always;
      }

      @page {
        size: A4;
        margin: 0;
      }
    }
  </style>
</svelte:head>

<div class="container mx-auto p-4 rtl">
  <!-- شريط الأدوات والتحكم -->
  <div class="controls bg-white p-4 rounded-lg shadow-md mb-6">
    <div class="flex justify-between items-center mb-4">
      <div>
        <h1 class="text-2xl font-bold">محرر مستندات Word</h1>
        <p class="text-gray-600">استخدم هذا المحرر لإنشاء مستندات رسمية بتنسيق Word باللغة العربية</p>
      </div>
      <div class="flex space-x-2 space-x-reverse">
        <button
          class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded flex items-center"
          on:click={printDocument}
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M5 4v3H4a2 2 0 00-2 2v3a2 2 0 002 2h1v2a2 2 0 002 2h6a2 2 0 002-2v-2h1a2 2 0 002-2V9a2 2 0 00-2-2h-1V4a2 2 0 00-2-2H7a2 2 0 00-2 2zm8 0H7v3h6V4zm0 8H7v4h6v-4z" clip-rule="evenodd" />
          </svg>
          طباعة المستند
        </button>

        <button
          class="bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded flex items-center"
          on:click={saveDocument}
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
            <path d="M7.707 10.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V6h5a2 2 0 012 2v7a2 2 0 01-2 2H4a2 2 0 01-2-2V8a2 2 0 012-2h5v5.586l-1.293-1.293zM9 4a1 1 0 012 0v2H9V4z" />
          </svg>
          حفظ المستند
        </button>

        <button
          class="bg-yellow-600 hover:bg-yellow-700 text-white py-2 px-4 rounded flex items-center"
          on:click={openDocument}
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
          </svg>
          فتح مستند
        </button>

        <button
          class="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded flex items-center"
          on:click={openSignatureModal}
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
            <path d="M17.414 2.586a2 2 0 00-2.828 0L7 10.172V13h2.828l7.586-7.586a2 2 0 000-2.828z" />
            <path fill-rule="evenodd" d="M2 6a2 2 0 012-2h4a1 1 0 010 2H4v10h10v-4a1 1 0 112 0v4a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" clip-rule="evenodd" />
          </svg>
          إرسال كرسالة رسمية موقعة
        </button>
      </div>
    </div>

    <div class="bg-blue-50 border border-blue-200 rounded-md p-4 mb-4">
      <div class="flex justify-between items-center">
        <h3 class="font-bold text-blue-800">تعليمات الاستخدام:</h3>
        <button
          class="bg-green-600 hover:bg-green-700 text-white py-1 px-3 rounded text-sm flex items-center"
          on:click={toggleInstructions}
        >
          {showInstructions ? 'إخفاء التعليمات' : 'إظهار التعليمات'}
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
            {#if showInstructions}
              <path fill-rule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clip-rule="evenodd" />
            {:else}
              <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
            {/if}
          </svg>
        </button>
      </div>

      <div class="instructions-content mt-3" class:hidden={!showInstructions}>
        <ol class="list-decimal list-inside text-sm text-blue-700 space-y-1">
          <li>أدخل عنوان المستند والرقم الإشاري والتاريخ واسم الكاتب</li>
          <li>يتم تحميل الشعارات تلقائياً من إعدادات المنظمة</li>
          <li>اختر حجم الخط ونوعه وتباعد الأسطر</li>
          <li>اكتب محتوى المستند في المحرر</li>
          <li>يمكنك إضافة صفحات جديدة عند الحاجة</li>
          <li>استخدم أدوات التنسيق لتنسيق النص وإضافة جداول وقوائم</li>
          <li>اضغط على زر "حفظ المستند" لحفظ المستند محلياً على جهازك</li>
          <li>اضغط على زر "فتح مستند" لفتح مستند محفوظ سابقاً</li>
          <li>اضغط على زر "طباعة المستند" لطباعة المستند أو حفظه كملف PDF</li>
        </ol>

        <div class="bg-yellow-50 border border-yellow-200 rounded-md p-3 mt-3">
          <p class="font-bold text-yellow-800">حل مشكلة الكتابة المعكوسة:</p>
          <ul class="list-disc list-inside mr-4 mt-1 space-y-1 text-sm text-yellow-800">
            <li>إذا ظهرت الكتابة معكوسة، اضغط على زر "إصلاح اتجاه النص" الموجود أسفل المحرر</li>
            <li>عند الكتابة، تأكد من الضغط على Enter لإنشاء فقرة جديدة بعد كل فقرة</li>
            <li>تجنب الكتابة المباشرة بعد الأرقام أو النصوص اللاتينية، بل ابدأ فقرة جديدة</li>
            <li>يمكنك تحديد النص ثم الضغط على Ctrl+Shift+L لتغيير اتجاه النص إلى اليمين</li>
          </ul>
        </div>

        <div class="bg-green-50 border border-green-200 rounded-md p-3 mt-3">
          <p class="font-bold text-green-800">حفظ واسترجاع المستندات:</p>
          <ul class="list-disc list-inside mr-4 mt-1 space-y-1 text-sm text-green-800">
            <li>يمكنك حفظ المستند محلياً على جهازك بصيغة JSON باستخدام زر "حفظ المستند"</li>
            <li>لاسترجاع مستند محفوظ سابقاً، اضغط على زر "فتح مستند" واختر ملف المستند</li>
            <li>يتم حفظ جميع بيانات المستند بما في ذلك العنوان والشعار والمحتوى وإعدادات التنسيق</li>
            <li>يمكنك مشاركة ملفات المستندات المحفوظة مع الآخرين لفتحها في المحرر</li>
            <li>للحصول على نسخة PDF من المستند، اضغط على زر "طباعة المستند" ثم اختر "حفظ كـ PDF"</li>
          </ul>
        </div>

        <div class="text-sm text-blue-700 mt-3">
          <p class="font-bold">نصائح لتحسين تجربة الكتابة:</p>
          <ul class="list-disc list-inside mr-4 mt-1 space-y-1">
            <li>المحرر يدعم الكتابة باللغة العربية من اليمين إلى اليسار بشكل كامل</li>
            <li>للحصول على أفضل النتائج، استخدم خط Tajawal أو Tahoma</li>
            <li>يمكنك استخدام اختصارات لوحة المفاتيح مثل Ctrl+B للنص الغامق و Ctrl+I للنص المائل</li>
            <li>عند إدخال أرقام أو نصوص لاتينية، قد يتغير اتجاه النص - استخدم زر "إصلاح اتجاه النص" لإصلاح ذلك</li>
          </ul>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
      <!-- معلومات المستند -->
      <div>
        <div class="mb-4">
          <label for="document-title" class="block text-gray-700 mb-2">عنوان المستند</label>
          <input
            type="text"
            id="document-title"
            bind:value={documentTitle}
            class="w-full p-2 border border-gray-300 rounded"
            placeholder="أدخل عنوان المستند"
          />
        </div>

        <div class="mb-4">
          <label for="document-ref" class="block text-gray-700 mb-2">الرقم الإشاري</label>
          <input
            type="text"
            id="document-ref"
            bind:value={documentRef}
            class="w-full p-2 border border-gray-300 rounded"
            placeholder="أدخل الرقم الإشاري للمستند"
          />
        </div>

        <div class="mb-4">
          <label for="document-date" class="block text-gray-700 mb-2">تاريخ المستند</label>
          <input
            type="date"
            id="document-date"
            bind:value={documentDate}
            class="w-full p-2 border border-gray-300 rounded"
          />
        </div>

        <div class="mb-4">
          <label for="document-author" class="block text-gray-700 mb-2">الكاتب</label>
          <input
            type="text"
            id="document-author"
            bind:value={documentAuthor}
            class="w-full p-2 border border-gray-300 rounded"
            placeholder="أدخل اسم كاتب المستند"
          />
        </div>
      </div>

      <!-- خيارات التنسيق -->
      <div>
        <div class="mb-4">
          <p class="block text-gray-700 mb-2">الشعارات</p>
          <div class="bg-gray-100 p-3 rounded">
            <div class="flex justify-between">
              {#if effectiveSecondLogoUrl}
                <div class="text-center">
                  <p class="text-xs text-gray-500 mb-1">الشعار الأيمن</p>
                  <img src={effectiveSecondLogoUrl} alt="الشعار الأيمن" class="h-16 mx-auto" />
                </div>
              {/if}

              {#if effectiveLogoUrl}
                <div class="text-center">
                  <p class="text-xs text-gray-500 mb-1">الشعار الأيسر</p>
                  <img src={effectiveLogoUrl} alt="الشعار الأيسر" class="h-16 mx-auto" />
                </div>
              {/if}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- التنقل بين الصفحات -->
  <div class="page-navigation flex justify-between items-center mb-4">
    <div>
      <button
        class="bg-gray-200 hover:bg-gray-300 py-1 px-3 rounded ml-2"
        on:click={prevPage}
        disabled={currentPage === 0}
      >
        الصفحة السابقة
      </button>
      <button
        class="bg-gray-200 hover:bg-gray-300 py-1 px-3 rounded"
        on:click={nextPage}
        disabled={currentPage === pages.length - 1}
      >
        الصفحة التالية
      </button>
    </div>

    <div class="flex items-center">
      <span>صفحة {currentPage + 1} من {pages.length}</span>
      <button
        class="bg-green-500 hover:bg-green-600 text-white py-1 px-3 rounded mr-2"
        on:click={addNewPage}
      >
        إضافة صفحة جديدة
      </button>
      <button
        class="bg-blue-500 hover:bg-blue-600 text-white py-1 px-3 rounded mr-2"
        on:click={fixTextDirection}
      >
        إصلاح اتجاه النص
      </button>
      <button
        class="bg-purple-500 hover:bg-purple-600 text-white py-1 px-3 rounded mr-2"
        on:click={scrollToDocument}
      >
        عرض المستند
      </button>
      <button
        class="bg-amber-500 hover:bg-amber-600 text-white py-1 px-3 rounded mr-2"
        on:click={toggleAdvancedTools}
      >
        {showAdvancedTools ? 'إخفاء أدوات التنسيق' : 'إظهار أدوات التنسيق'}
      </button>
    </div>
  </div>

  <!-- أدوات التنسيق المتقدمة -->
  {#if showAdvancedTools}
    <div class="advanced-tools bg-gray-100 border border-gray-300 rounded-md p-4 mb-4">
      <h3 class="font-bold text-gray-800 mb-3">أدوات التنسيق المتقدمة</h3>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- تنسيق النص -->
        <div class="formatting-section">
          <h4 class="font-bold text-gray-700 mb-2">تنسيق النص</h4>

          <div class="flex flex-wrap gap-2 mb-3">
            <button
              class="formatting-button"
              title="غامق"
              aria-label="غامق"
              on:click={() => applyTextFormat('bold', fontWeight === 'bold' ? 'normal' : 'bold')}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
                <path d="M6 4h8a4 4 0 0 1 4 4 4 4 0 0 1-4 4H6z"></path>
                <path d="M6 12h9a4 4 0 0 1 4 4 4 4 0 0 1-4 4H6z"></path>
              </svg>
              <span class="sr-only">غامق</span>
            </button>

            <button
              class="formatting-button"
              title="مائل"
              aria-label="مائل"
              on:click={() => applyTextFormat('italic', fontStyle === 'italic' ? 'normal' : 'italic')}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
                <line x1="19" y1="4" x2="10" y2="4"></line>
                <line x1="14" y1="20" x2="5" y2="20"></line>
                <line x1="15" y1="4" x2="9" y2="20"></line>
              </svg>
              <span class="sr-only">مائل</span>
            </button>

            <button
              class="formatting-button"
              title="تسطير"
              aria-label="تسطير"
              on:click={() => applyTextFormat('underline', textDecoration === 'underline' ? 'none' : 'underline')}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
                <path d="M6 3v7a6 6 0 0 0 6 6 6 6 0 0 0 6-6V3"></path>
                <line x1="4" y1="21" x2="20" y2="21"></line>
              </svg>
              <span class="sr-only">تسطير</span>
            </button>

            <div class="flex items-center">
              <label for="text-color" class="ml-2 text-sm">لون النص:</label>
              <input
                type="color"
                id="text-color"
                value={textColor}
                class="w-8 h-8 border-0"
                on:change={(e) => {
                  const target = e.target as HTMLInputElement;
                  applyTextFormat('color', target.value);
                }}
              />
            </div>

            <div class="flex items-center">
              <label for="bg-color" class="ml-2 text-sm">لون الخلفية:</label>
              <input
                type="color"
                id="bg-color"
                value={backgroundColor === 'transparent' ? '#ffffff' : backgroundColor}
                class="w-8 h-8 border-0"
                on:change={(e) => {
                  const target = e.target as HTMLInputElement;
                  applyTextFormat('background', target.value);
                }}
              />
            </div>
          </div>

          <div class="flex flex-wrap gap-2 mb-3">
            <button
              class="formatting-button"
              title="محاذاة لليمين"
              aria-label="محاذاة لليمين"
              on:click={() => applyTextFormat('align', 'right')}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
                <line x1="21" y1="6" x2="3" y2="6"></line>
                <line x1="21" y1="12" x2="9" y2="12"></line>
                <line x1="21" y1="18" x2="7" y2="18"></line>
              </svg>
              <span class="sr-only">محاذاة لليمين</span>
            </button>

            <button
              class="formatting-button"
              title="توسيط"
              aria-label="توسيط"
              on:click={() => applyTextFormat('align', 'center')}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
                <line x1="21" y1="6" x2="3" y2="6"></line>
                <line x1="18" y1="12" x2="6" y2="12"></line>
                <line x1="19" y1="18" x2="5" y2="18"></line>
              </svg>
              <span class="sr-only">توسيط</span>
            </button>

            <button
              class="formatting-button"
              title="محاذاة لليسار"
              aria-label="محاذاة لليسار"
              on:click={() => applyTextFormat('align', 'left')}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
                <line x1="3" y1="6" x2="21" y2="6"></line>
                <line x1="3" y1="12" x2="15" y2="12"></line>
                <line x1="3" y1="18" x2="17" y2="18"></line>
              </svg>
              <span class="sr-only">محاذاة لليسار</span>
            </button>

            <button
              class="formatting-button"
              title="ضبط"
              aria-label="ضبط"
              on:click={() => applyTextFormat('align', 'justify')}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
                <line x1="3" y1="6" x2="21" y2="6"></line>
                <line x1="3" y1="12" x2="21" y2="12"></line>
                <line x1="3" y1="18" x2="21" y2="18"></line>
              </svg>
              <span class="sr-only">ضبط</span>
            </button>
          </div>

          <!-- التحكم في حجم الخط -->
          <div class="font-size-control mb-3">
            <h4 class="font-bold text-gray-700 mb-2">حجم الخط</h4>
            <div class="flex flex-wrap items-center">
              <div class="flex items-center mb-2 ml-4">
                <button
                  class="formatting-button"
                  title="تصغير الخط"
                  aria-label="تصغير الخط"
                  on:click={() => {
                    if (fontSize > 8) {
                      fontSize -= 1;
                      applyTextFormat('fontSize', `${fontSize}pt`);
                    }
                  }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
                    <path d="M4 7L4 4L16 4L16 7"></path>
                    <path d="M10 20L10 4"></path>
                    <path d="M18 14L20 14L20 20L14 20L14 14L16 14"></path>
                    <path d="M20 14L14 20"></path>
                  </svg>
                  <span class="sr-only">تصغير الخط</span>
                </button>

                <div class="mx-2 font-size-display font-bold">
                  <span>{fontSize}pt</span>
                </div>

                <button
                  class="formatting-button"
                  title="تكبير الخط"
                  aria-label="تكبير الخط"
                  on:click={() => {
                    if (fontSize < 36) {
                      fontSize += 1;
                      applyTextFormat('fontSize', `${fontSize}pt`);
                    }
                  }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
                    <path d="M16 4L16 7L4 7L4 4L16 4"></path>
                    <path d="M10 20L10 4"></path>
                    <path d="M14 14L14 20L20 20L20 14L14 14"></path>
                    <path d="M14 20L20 14"></path>
                  </svg>
                  <span class="sr-only">تكبير الخط</span>
                </button>
              </div>

              <div class="flex items-center mb-2 ml-4">
                <input
                  type="range"
                  min="8"
                  max="36"
                  step="1"
                  bind:value={fontSize}
                  class="w-32"
                  on:change={() => applyTextFormat('fontSize', `${fontSize}pt`)}
                />
              </div>

              <div class="flex flex-wrap gap-2 mb-2">
                <button
                  class="font-size-preset-btn {fontSize === 10 ? 'active' : ''}"
                  on:click={() => {
                    fontSize = 10;
                    applyTextFormat('fontSize', `${fontSize}pt`);
                  }}
                >
                  10pt
                </button>
                <button
                  class="font-size-preset-btn {fontSize === 12 ? 'active' : ''}"
                  on:click={() => {
                    fontSize = 12;
                    applyTextFormat('fontSize', `${fontSize}pt`);
                  }}
                >
                  12pt
                </button>
                <button
                  class="font-size-preset-btn {fontSize === 14 ? 'active' : ''}"
                  on:click={() => {
                    fontSize = 14;
                    applyTextFormat('fontSize', `${fontSize}pt`);
                  }}
                >
                  14pt
                </button>
                <button
                  class="font-size-preset-btn {fontSize === 16 ? 'active' : ''}"
                  on:click={() => {
                    fontSize = 16;
                    applyTextFormat('fontSize', `${fontSize}pt`);
                  }}
                >
                  16pt
                </button>
                <button
                  class="font-size-preset-btn {fontSize === 18 ? 'active' : ''}"
                  on:click={() => {
                    fontSize = 18;
                    applyTextFormat('fontSize', `${fontSize}pt`);
                  }}
                >
                  18pt
                </button>
                <button
                  class="font-size-preset-btn {fontSize === 24 ? 'active' : ''}"
                  on:click={() => {
                    fontSize = 24;
                    applyTextFormat('fontSize', `${fontSize}pt`);
                  }}
                >
                  24pt
                </button>
              </div>
            </div>
          </div>

          <!-- التحكم في نوع الخط -->
          <div class="font-family-control mb-3">
            <h4 class="font-bold text-gray-700 mb-2">نوع الخط</h4>
            <div class="flex flex-wrap gap-2 mb-2">
              {#each arabicFonts as font}
                <button
                  class="font-family-btn {fontFamily.includes(font.name) ? 'active' : ''}"
                  style="font-family: {font.value};"
                  on:click={() => {
                    changeFontFamily(font.value);
                  }}
                >
                  {font.name}
                </button>
              {/each}
            </div>
          </div>

          <!-- التحكم في تباعد الأسطر -->
          <div class="line-height-control mb-3">
            <h4 class="font-bold text-gray-700 mb-2">تباعد الأسطر</h4>
            <div class="flex flex-wrap gap-2 mb-2">
              {#each lineHeightOptions as option}
                <button
                  class="line-height-btn {lineHeight === option.value ? 'active' : ''}"
                  on:click={() => {
                    changeLineHeight(option.value);
                  }}
                >
                  {option.name}
                </button>
              {/each}
            </div>

            <div class="flex items-center mb-2">
              <span class="ml-2 text-sm">تباعد مخصص:</span>
              <input
                type="range"
                min="1"
                max="3"
                step="0.1"
                bind:value={lineHeight}
                class="w-32 ml-2"
                on:change={() => changeLineHeight(lineHeight)}
              />
              <span class="mr-2 text-sm">{lineHeight.toFixed(1)}</span>
            </div>
          </div>

          <div class="mb-3">
            <h4 class="font-bold text-gray-700 mb-2">خيارات المستند</h4>
            <div class="flex items-center mb-2">
              <label class="flex items-center">
                <input type="checkbox" bind:checked={showPageNumbers} class="ml-2">
                <span class="text-gray-700">إظهار أرقام الصفحات</span>
              </label>
            </div>
          </div>

          <div class="text-sm text-gray-600 mb-3 w-full">
            <p>ملاحظة: حدد النص أولاً ثم غير التنسيق لتطبيقه على النص المحدد فقط. إذا لم تحدد نصاً، سيتم تطبيق التنسيق على الفقرة الحالية فقط.</p>
          </div>
        </div>

        <!-- إدراج عناصر -->
        <div class="insert-section">
          <h4 class="font-bold text-gray-700 mb-2">إدراج عناصر</h4>

          <div class="mb-3">
            <button
              class="bg-blue-500 hover:bg-blue-600 text-white py-1 px-3 rounded ml-2"
              on:click={insertTable}
            >
              إدراج جدول
            </button>

            <div class="flex items-center mt-2">
              <label for="table-rows" class="ml-2 text-sm">عدد الصفوف:</label>
              <input
                type="number"
                id="table-rows"
                min="1"
                max="10"
                bind:value={tableRows}
                class="w-16 p-1 border border-gray-300 rounded"
              />

              <label for="table-cols" class="mr-3 ml-2 text-sm">عدد الأعمدة:</label>
              <input
                type="number"
                id="table-cols"
                min="1"
                max="10"
                bind:value={tableCols}
                class="w-16 p-1 border border-gray-300 rounded"
              />
            </div>
          </div>

          <div class="flex flex-wrap gap-2">
            <button
              class="bg-green-500 hover:bg-green-600 text-white py-1 px-3 rounded"
              on:click={() => insertList('ul')}
            >
              قائمة نقطية
            </button>

            <button
              class="bg-green-500 hover:bg-green-600 text-white py-1 px-3 rounded"
              on:click={() => insertList('ol')}
            >
              قائمة رقمية
            </button>
          </div>
        </div>
      </div>

      <div class="mt-3 text-sm text-gray-600">
        <p>ملاحظة: حدد النص أولاً ثم اضغط على أداة التنسيق المطلوبة لتطبيقها على النص المحدد.</p>
      </div>
    </div>
  {/if}

  <!-- أزرار التنقل الثابتة -->
  <div class="fixed-navigation">
    <button
      class="navigation-button top-button"
      title="العودة إلى الأعلى"
      aria-label="العودة إلى الأعلى"
      on:click={scrollToTop}
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
        <polyline points="18 15 12 9 6 15"></polyline>
      </svg>
      <span class="sr-only">العودة إلى الأعلى</span>
    </button>

    <button
      class="navigation-button bottom-button"
      title="الانتقال إلى الأسفل"
      aria-label="الانتقال إلى الأسفل"
      on:click={scrollToBottom}
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
        <polyline points="6 9 12 15 18 9"></polyline>
      </svg>
      <span class="sr-only">الانتقال إلى الأسفل</span>
    </button>
  </div>

  <!-- حاوية الصفحات -->
  <div class="page-container">
    {#each pages as page, i}
      {#if i === currentPage}
        <div class="page bg-white shadow-lg mx-auto mb-8">
          <!-- ترويسة المستند -->
          <div class="page-header">
            <div class="header-container">
              {#if effectiveSecondLogoUrl}
                <div class="second-logo">
                  <img src={effectiveSecondLogoUrl} alt="الشعار الأيمن" />
                </div>
              {/if}

              <div class="ministry-info">
                <h2 class="ministry-name">{organizationName}</h2>
                <h3 class="ministry-dept">{ministryName}</h3>
              </div>

              {#if effectiveLogoUrl}
                <div class="logo">
                  <img src={effectiveLogoUrl} alt="الشعار الأيسر" />
                </div>
              {/if}
            </div>

            <div class="document-info">
              {#if documentRef}
                <div class="document-ref">
                  <span>الرقم الإشاري: </span>
                  <span>{documentRef}</span>
                </div>
              {/if}

              {#if documentDate}
                <div class="document-date">
                  <span>التاريخ: </span>
                  <span>{documentDate}</span>
                </div>
              {/if}
            </div>

            <h1 class="document-title">{documentTitle}</h1>
          </div>

          <!-- محتوى المستند -->
          <div
            class="page-content"
            contenteditable="true"
            on:input={(e) => handleContentChange(e, i)}
            style="font-size: {fontSize}px; line-height: {lineHeight}; font-family: {fontFamily};"
            dir="rtl"
            on:focus={(e) => {
              // عند التركيز على العنصر، تأكد من أن له خصائص RTL
              const target = e.target as HTMLDivElement;
              if (!target.hasAttribute('dir')) {
                target.setAttribute('dir', 'rtl');
              }

              // إذا كان المحتوى هو النص الافتراضي، قم بمسحه
              if (i === 0 && !page.content && target.textContent?.trim() === 'ابدأ الكتابة هنا...') {
                target.innerHTML = '<p dir="rtl" style="text-align: right;"></p>';
                // ضع المؤشر في بداية الفقرة
                const selection = window.getSelection();
                const range = document.createRange();
                const p = target.querySelector('p');
                if (p && selection) {
                  range.setStart(p, 0);
                  range.collapse(true);
                  selection.removeAllRanges();
                  selection.addRange(range);
                }
              }
            }}
          >
            {#if i === 0 && !page.content}
              <p dir="rtl" style="text-align: right;">ابدأ الكتابة هنا...</p>
            {:else}
              {@html page.content || '<p dir="rtl" style="text-align: right;"></p>'}
            {/if}
          </div>

          <!-- تذييل الصفحة -->
          <div class="page-footer">
            <div class="footer-meta">
              {#if showPageNumbers}
                <div class="page-number">
                  صفحة {i + 1} من {pages.length}
                </div>
              {/if}
            </div>

            <div class="footer-line">
              <div class="red-line"></div>
              {#if organizationAddress || organizationPhone || organizationEmail || organizationWebsite}
                <div class="footer-contact">
                  {#if organizationAddress}
                    <span class="footer-address">{organizationAddress}</span>
                  {/if}
                  {#if organizationPhone}
                    <span class="footer-phone">{organizationPhone}</span>
                  {/if}
                  {#if organizationEmail}
                    <span class="footer-email">{organizationEmail}</span>
                  {/if}
                  {#if organizationWebsite}
                    <span class="footer-website">{organizationWebsite}</span>
                  {/if}
                </div>
              {/if}
            </div>
          </div>
        </div>
      {/if}
    {/each}
  </div>
</div>

<style>
  /* تنسيقات الصفحة A4 */
  .page {
    width: 210mm;
    min-height: 297mm;
    padding: 15mm 20mm 50mm; /* زيادة الهامش السفلي من 45mm إلى 50mm لإفساح المجال للتذييل */
    box-sizing: border-box;
    direction: rtl;
    position: relative;
    background-color: white;
    page-break-after: always;
    font-family: 'Tajawal', Tahoma, Arial, sans-serif;
    text-align: right;
    overflow: hidden; /* منع تجاوز المحتوى لحدود الصفحة */
  }

  /* ترويسة المستند */
  .page-header {
    margin-bottom: 15mm; /* تقليل الهامش السفلي من 20mm إلى 15mm */
  }

  .header-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 5mm;
    position: relative;
  }

  .ministry-info {
    text-align: center;
    margin: 0 auto;
  }

  .ministry-name {
    font-size: 18pt;
    font-weight: bold;
    margin: 0;
    color: #000;
  }

  .ministry-dept {
    font-size: 16pt;
    font-weight: bold;
    margin: 5px 0 0;
    color: #000;
  }

  .logo {
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
  }

  .second-logo {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
  }

  .logo img, .second-logo img {
    max-height: 25mm; /* تقليل ارتفاع الشعار من 30mm إلى 25mm */
    max-width: 100%;
  }

  .document-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8mm; /* تقليل الهامش السفلي من 10mm إلى 8mm */
    font-size: 12pt;
  }

  .document-title {
    text-align: center;
    font-size: 18pt;
    font-weight: bold;
    margin-bottom: 8mm; /* تقليل الهامش السفلي من 10mm إلى 8mm */
    margin-top: 4mm; /* إضافة هامش علوي */
  }

  /* محتوى المستند */
  .page-content {
    min-height: 175mm; /* تقليل ارتفاع المحتوى لإفساح المجال للتذييل */
    max-height: 175mm; /* تحديد الحد الأقصى لارتفاع المحتوى */
    outline: none;
    white-space: pre-wrap;
    text-align: right;
    overflow-y: auto; /* إضافة شريط تمرير عند الحاجة */
  }

  .page-content p {
    margin-bottom: 8pt;
    text-align: right;
  }

  .page-content * {
    direction: rtl;
    text-align: right;
  }

  /* تذييل الصفحة */
  .page-footer {
    position: absolute;
    bottom: 5mm; /* إنزال التذييل أكثر من 10mm إلى 5mm */
    left: 20mm;
    right: 20mm;
    display: flex;
    flex-direction: column;
    font-size: 10pt;
    color: #666;
    background-color: rgba(255, 255, 255, 0.95); /* إضافة خلفية شبه شفافة */
    padding-top: 5mm; /* إضافة تباعد علوي */
    border-top: 1px solid #eee; /* إضافة خط فاصل */
    z-index: 10; /* ضمان ظهور التذييل فوق المحتوى */
  }

  .footer-line {
    position: relative;
    margin-top: 5mm;
    padding-top: 2mm;
  }

  .red-line {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px; /* زيادة سمك الخط من 1px إلى 2px */
    background-color: #e53e3e; /* لون أحمر */
    z-index: 5;
    box-shadow: 0 1px 2px rgba(229, 62, 62, 0.3); /* إضافة ظل خفيف للخط */
  }

  .footer-contact {
    display: flex;
    justify-content: center;
    flex-wrap: nowrap; /* منع التفاف النص */
    gap: 20px;
    font-size: 9pt; /* زيادة حجم الخط من 8pt إلى 9pt */
    line-height: 1.5;
    padding-top: 3mm;
    text-align: center;
    color: #333; /* لون أغمق للنص */
    font-weight: 500; /* خط أكثر سمكاً */
  }

  .footer-contact span:not(:last-child)::after {
    content: "•";
    margin-right: 20px;
    color: #e53e3e; /* نفس لون الخط الأحمر */
  }

  .footer-meta {
    display: flex;
    justify-content: space-between;
    margin-top: 3mm;
    padding-top: 3mm;
  }

  /* تنسيقات إضافية */
  .rtl {
    direction: rtl;
    text-align: right;
  }

  .container input,
  .container button {
    font-family: 'Tajawal', Tahoma, Arial, sans-serif;
  }

  /* أزرار التنقل الثابتة */
  .fixed-navigation {
    position: fixed;
    left: 20px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    gap: 10px;
    z-index: 1000;
  }

  .navigation-button {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(79, 70, 229, 0.8);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
  }

  .navigation-button:hover {
    background-color: rgba(79, 70, 229, 1);
    transform: scale(1.1);
  }

  .navigation-button:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.3);
  }

  /* عناصر مخفية للقراء الشاشة */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
  }

  /* أزرار التنسيق */
  .formatting-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border: 1px solid #ccc;
    border-radius: 4px;
    background-color: white;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .formatting-button:hover {
    background-color: #f0f0f0;
    border-color: #999;
  }

  .formatting-button:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
  }

  .font-size-preset-btn {
    background-color: #f0f0f0;
    border: 1px solid #ccc;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .font-size-preset-btn:hover {
    background-color: #e0e0e0;
    border-color: #999;
  }

  .font-size-preset-btn.active {
    background-color: #3b82f6;
    border-color: #2563eb;
    color: white;
  }

  .font-family-btn {
    background-color: #f0f0f0;
    border: 1px solid #ccc;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 80px;
  }

  .font-family-btn:hover {
    background-color: #e0e0e0;
    border-color: #999;
  }

  .font-family-btn.active {
    background-color: #3b82f6;
    border-color: #2563eb;
    color: white;
  }

  .line-height-btn {
    background-color: #f0f0f0;
    border: 1px solid #ccc;
    border-radius: 4px;
    padding: 4px 12px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 70px;
  }

  .line-height-btn:hover {
    background-color: #e0e0e0;
    border-color: #999;
  }

  .line-height-btn.active {
    background-color: #3b82f6;
    border-color: #2563eb;
    color: white;
  }

  .font-size-display {
    min-width: 40px;
    text-align: center;
  }

  .advanced-tools {
    direction: rtl;
  }

  /* أنماط تعليمات الاستخدام */
  .instructions-content {
    max-height: 1000px;
    overflow: hidden;
    transition: max-height 0.5s ease-in-out, opacity 0.5s ease-in-out;
    opacity: 1;
    animation: fadeIn 0.5s ease-in-out;
  }

  .instructions-content.hidden {
    max-height: 0;
    opacity: 0;
    margin: 0;
    padding: 0;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* أنماط الجداول */
  :global(.editable-table) {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 10mm;
    direction: rtl;
  }

  :global(.editable-table th),
  :global(.editable-table td) {
    border: 1px solid #000;
    padding: 8px;
    text-align: right;
    min-width: 50px;
  }

  :global(.editable-table th) {
    background-color: #f0f0f0;
    font-weight: bold;
  }

  :global(.table-container) {
    margin-bottom: 15mm;
  }

  :global(.table-controls) {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    justify-content: center;
    margin-top: 5mm;
    margin-bottom: 5mm;
    direction: rtl;
  }

  :global(.table-control-btn) {
    background-color: #f0f0f0;
    border: 1px solid #ccc;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  :global(.table-control-btn:hover) {
    background-color: #e0e0e0;
    border-color: #999;
  }

  :global(.table-control-btn.delete-btn) {
    background-color: #fee2e2;
    border-color: #fca5a5;
    color: #b91c1c;
  }

  :global(.table-control-btn.delete-btn:hover) {
    background-color: #fecaca;
    border-color: #ef4444;
  }

  @media print {
    .fixed-navigation, .advanced-tools {
      display: none;
    }
  }

  /* تنسيقات الطباعة */
  @media print {
    @page {
      size: 210mm 297mm;
      margin: 0;
    }

    .page {
      box-shadow: none;
      margin: 0;
      padding: 15mm 20mm 50mm; /* تطبيق نفس الهوامش المعدلة عند الطباعة */
      page-break-after: always;
      width: 210mm;
      height: 297mm;
      overflow: hidden;
    }

    /* تطبيق نفس التعديلات على ترويسة المستند عند الطباعة */
    .page-header {
      margin-bottom: 15mm;
    }

    .header-container {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 5mm;
      position: relative;
    }

    .ministry-info {
      text-align: center;
      margin: 0 auto;
    }

    .ministry-name {
      font-size: 18pt;
      font-weight: bold;
      margin: 0;
    }

    .ministry-dept {
      font-size: 16pt;
      font-weight: bold;
      margin: 5px 0 0;
    }

    .logo {
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
    }

    .second-logo {
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
    }

    .logo img, .second-logo img {
      max-height: 25mm;
    }

    .document-info {
      margin-bottom: 8mm;
    }

    .document-title {
      margin-bottom: 8mm;
    }

    .page-content {
      min-height: 175mm; /* تقليل ارتفاع المحتوى لإفساح المجال للتذييل */
      max-height: 175mm; /* تحديد الحد الأقصى لارتفاع المحتوى */
      overflow: visible;
    }

    .page-footer {
      position: absolute;
      bottom: 5mm; /* إنزال التذييل أكثر من 10mm إلى 5mm */
      left: 20mm;
      right: 20mm;
      background-color: white;
      padding-top: 5mm;
      border-top: 1px solid #eee;
      z-index: 10;
    }

    /* تنسيقات تذييل الصفحة عند الطباعة */
    .page-footer {
      position: absolute;
      bottom: 5mm; /* إنزال التذييل أكثر من 10mm إلى 5mm */
      left: 20mm;
      right: 20mm;
      display: flex;
      flex-direction: column;
      font-size: 10pt;
      color: #666;
    }

    .footer-line {
      position: relative;
      margin-top: 5mm;
      padding-top: 2mm;
    }

    .red-line {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 2px; /* زيادة سمك الخط من 1px إلى 2px */
      background-color: #e53e3e; /* لون أحمر */
      z-index: 5;
      box-shadow: 0 1px 2px rgba(229, 62, 62, 0.3); /* إضافة ظل خفيف للخط */
    }

    .footer-contact {
      display: flex;
      justify-content: center;
      flex-wrap: nowrap; /* منع التفاف النص */
      gap: 20px;
      font-size: 9pt; /* زيادة حجم الخط من 8pt إلى 9pt */
      line-height: 1.5;
      padding-top: 3mm;
      text-align: center;
      color: #333; /* لون أغمق للنص */
      font-weight: 500; /* خط أكثر سمكاً */
    }

    .footer-contact span:not(:last-child)::after {
      content: "•";
      margin-right: 20px;
      color: #e53e3e; /* نفس لون الخط الأحمر */
    }

    .footer-meta {
      display: flex;
      justify-content: space-between;
    }

    .container {
      direction: rtl;
      text-align: right;
      font-family: 'Tajawal', Tahoma, Arial, sans-serif;
    }
  }

  /* تنسيقات نافذة التوقيع */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
  }

  .modal-content {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    padding: 20px;
    direction: rtl;
  }
</style>

<!-- نافذة إرسال المستند للتوقيع -->
{#if showSignatureModal}
  <div
    class="modal-overlay"
    on:click|self={() => showSignatureModal = false}
    on:keydown={(e) => e.key === 'Escape' && (showSignatureModal = false)}
    role="dialog"
    aria-modal="true"
    aria-labelledby="signature-modal-title"
    tabindex="-1"
  >
    <div class="modal-content">
      <div class="flex justify-between items-center mb-4">
        <h2 id="signature-modal-title" class="text-xl font-bold">إرسال كرسالة رسمية موقعة</h2>
        <button
          class="text-gray-500 hover:text-gray-700"
          on:click={() => showSignatureModal = false}
          aria-label="إغلاق"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <div class="mb-4">
        <label for="signer-select" class="block text-gray-700 mb-2">اختر الموقع</label>
        <select
          id="signer-select"
          bind:value={selectedSignerId}
          class="w-full p-2 border border-gray-300 rounded"
        >
          <option value="">-- اختر الموقع --</option>
          {#each signersList as signer}
            <option value={signer.id}>
              {signer.full_name}
              {#if signer.role}
                ({signer.role})
              {/if}
              {#if signer.unit_name}
                - {signer.unit_name}
              {/if}
            </option>
          {/each}
        </select>

        {#if signersList.length === 0}
          <p class="text-yellow-600 text-sm mt-2">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline-block ml-1" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
            لا يوجد مستخدمين لديهم صلاحية التوقيع. يرجى التواصل مع المشرف لإضافة صلاحيات التوقيع من لوحة تحكم المدير.
          </p>
        {/if}

        {#if isAdmin}
          <div class="mt-2 text-blue-600 text-sm">
            <a href="/dashboard/admin/signature-permissions" class="flex items-center hover:underline">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd" />
              </svg>
              إدارة صلاحيات التوقيع الإلكتروني
            </a>
          </div>
        {/if}
      </div>

      <div class="mb-4">
        <label class="flex items-center">
          <input
            type="checkbox"
            bind:checked={passwordProtected}
            class="ml-2"
          />
          <span>حماية التوقيع بكلمة مرور</span>
        </label>
      </div>

      {#if passwordProtected}
        <div class="mb-4">
          <label for="signature-password" class="block text-gray-700 mb-2">كلمة مرور التوقيع</label>
          <input
            type="password"
            id="signature-password"
            bind:value={signaturePassword}
            class="w-full p-2 border border-gray-300 rounded"
            placeholder="أدخل كلمة مرور للتوقيع"
          />
        </div>
      {/if}

      <div class="flex justify-end mt-4">
        <button
          class="bg-gray-300 hover:bg-gray-400 text-gray-800 py-2 px-4 rounded ml-2"
          on:click={() => showSignatureModal = false}
        >
          إلغاء
        </button>
        <button
          class="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded"
          on:click={sendDocumentForSignature}
          disabled={isSending || !selectedSignerId}
        >
          {isSending ? 'جاري الإرسال...' : 'إرسال للتوقيع'}
        </button>
      </div>
    </div>
  </div>
{/if}
